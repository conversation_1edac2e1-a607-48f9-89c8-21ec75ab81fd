#!/bin/bash
PAIRS="ASTER/USDT:USDT" #"GALA/USDT:USDT" # MNT/USDT:USDT HYPE/USDT:USDT" # "DOT/USDT:USDT GALA/USDT:USDT" #UNI/USDT:USDT ETH/USDT:USDT SOL/USDT:USDT ENA/USDT:USDT HYPE/USDT:USDT BTC/USDT:USDT"
TF="3m 5m" #15m 5m 1h 2h 4h 1d"
TM="futures"
DAYS="700"
EXCHANGE="bybit"

docker compose run --rm freqtrade download-data \
  --timeframe ${TF} \
  --trading-mode ${TM} \
  --exchange $EXCHANGE \
  --days ${DAYS} \
  --erase \
  --pairs ${PAIRS}
