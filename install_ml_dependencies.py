#!/usr/bin/env python3
"""
Install required dependencies for the ML trading strategies.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def main():
    """Install all required packages."""
    print("🔧 Installing ML Trading Strategy Dependencies")
    print("=" * 50)
    
    # Core ML packages
    packages = [
        "torch",
        "prophet",
        "arch",  # For GARCH models
        "scikit-learn",
        "numpy",
        "pandas",
        "ta-lib",  # Technical analysis
        "technical",
        "matplotlib",
        "seaborn",
        "plotly",
        "jupyter",
        "tensorboard"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"\n📦 Installing {package}...")
        if not install_package(package):
            failed_packages.append(package)
    
    print("\n" + "=" * 50)
    print("📋 INSTALLATION SUMMARY")
    print("=" * 50)
    
    if failed_packages:
        print(f"❌ Failed to install: {', '.join(failed_packages)}")
        print("\n💡 Try installing failed packages manually:")
        for pkg in failed_packages:
            print(f"   pip install {pkg}")
    else:
        print("✅ All packages installed successfully!")
    
    print("\n🚀 You can now run the ML trading strategies!")
    print("\nNext steps:")
    print("1. Run: python backtest_lstm_strategy.py")
    print("2. Check the generated reports")
    print("3. Optimize parameters if needed")

if __name__ == "__main__":
    main()
