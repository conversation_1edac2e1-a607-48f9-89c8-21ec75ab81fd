{
  "trading_mode": "futures",
  "margin_mode": "isolated",
  "max_open_trades": -1,
  "stake_currency": "USDT",
  "stake_amount": 2000,
  "tradable_balance_ratio": 0.99,
  "fiat_display_currency": "USD",
  "dry_run": true,
  "strategy": "ExampleLSTMStrategy",
  "freqaimodel": "PyTorchLSTMRegressor",
  "dataformat_ohlcv": "json",
  "dataformat_trades": "jsongz",
  "cancel_open_orders_on_exit": false,
  "dry_run_wallet": 8000,
  "unfilledtimeout": {
    "entry": 10,
    "exit": 30
  },
  "entry_pricing": {
    "price_side": "same",
    "use_order_book": true,
    "order_book_top": 1,
    "price_last_balance": 0.0,
    "check_depth_of_market": {
      "enabled": false,
      "bids_to_ask_delta": 1
    }
  },
  "exit_pricing": {
    "price_side": "other",
    "use_order_book": true,
    "order_book_top": 1
  },
  "exchange": {
    "name": "binance",
    "sandbox": false,
    "key": "",
    "secret": "",
    "ccxt_config": {
      "enableRateLimit": true,
      "rateLimit": 50
    },
    "ccxt_async_config": {
      "enableRateLimit": true,
      "rateLimit": 50
    },
    "pair_whitelist": [
      "BTC/USDT:USDT",
      "ETH/USDT:USDT",
      "LTC/USDT:USDT",
      "BNB/USDT:USDT",
      "XRP/USDT:USDT",
      "ADA/USDT:USDT",
      "DOT/USDT:USDT",
      "SOL/USDT:USDT",
      "LINK/USDT:USDT",
      "AVAX/USDT:USDT"
    ],
    "pair_blacklist": []
  },
  "pairlists": [
    {
      "method": "StaticPairList"
    },
    {
      "method": "ShuffleFilter",
      "shuffle_frequency": "candle",
      "seed": 42
    }
  ],
  "freqai": {
    "enabled": true,
    "identifier": "torch-lstm12",
    "train_period_days": 120,
    "fit_live_predictions_candles": 24,
    "backtest_period_days": 30,
    "expiration_hours": 4,
    "live_retrain_hours": 4,
    "purge_old_models": 2,
    "save_backtest_models": true,
    "write_metrics_to_disk": true,
    "activate_tensorboard": true,
    "feature_parameters": {
      "include_corr_pairlist": [
        "BTC/USDT:USDT",
        "ETH/USDT:USDT"
      ],
      "include_timeframes": [
        "2h",
        "4h"
      ],
      "label_period_candles": 12,
      "include_shifted_candidates": 12,
      "DI_threshold": 10,
      "weight_factor": 0.5,
      "indicator_periods_candles": [
        10,
        20
      ],
      "noise_standard_deviation": 0.01,
      "buffer_train_data_candles": 20
    },
    "data_split_parameters": {
      "test_size": 0.2,
      "random_state": 42,
      "shuffle": false
    },
    "model_training_parameters": {
      "learning_rate": 3e-3,
      "trainer_kwargs": {
        "n_steps": null,
        "batch_size": 32,
        "n_epochs": 10,
      },
      "model_kwargs": {
        "num_lstm_layers": 3,
        "hidden_dim": 128,
        "dropout_percent": 0.4,
        "window_size": 5
      }
    }
  },
  //telegram and api
}
