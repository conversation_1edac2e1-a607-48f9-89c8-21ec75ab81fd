import logging
import warnings
from typing import Any, Dict, Tu<PERSON>, List
import numpy as np
import pandas as pd
from pandas import DataFrame
import numpy.typing as npt
from sklearn.ensemble import VotingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler

try:
    import torch
    from freqtrade.freqai.torch.PyTorchLSTMRegressor import PyTorchLSTMRegressor
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False
    PyTorchLSTMRegressor = None

try:
    from .ProphetRegressor import ProphetRegressor
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False
    ProphetRegressor = None

try:
    from .GARCHVolatilityModel import GARCHVolatilityModel
    GARCH_AVAILABLE = True
except ImportError:
    GARCH_AVAILABLE = False
    GARCHVolatilityModel = None

from freqtrade.freqai.base_models.BaseRegressionModel import BaseRegressionModel
from freqtrade.freqai.data_kitchen import FreqaiDataKitchen

logger = logging.getLogger(__name__)

class EnsembleMLRegressor(BaseRegressionModel):
    """
    Ensemble machine learning model combining LSTM, Prophet, and GARCH models.
    
    This model creates a sophisticated ensemble that leverages:
    - LSTM for sequential pattern recognition
    - Prophet for trend and seasonality analysis
    - GARCH for volatility modeling
    - Meta-learner for optimal combination
    
    Configuration example:
    "model_training_parameters": {
        "ensemble_method": "weighted_average",
        "lstm_weight": 0.4,
        "prophet_weight": 0.4,
        "garch_weight": 0.2,
        "use_meta_learner": True,
        "meta_learner": "linear_regression",
        "lstm_config": {
            "learning_rate": 3e-4,
            "model_kwargs": {
                "num_lstm_layers": 2,
                "hidden_dim": 64,
                "dropout_percent": 0.3
            }
        },
        "prophet_config": {
            "changepoint_prior_scale": 0.05,
            "seasonality_mode": "multiplicative"
        },
        "garch_config": {
            "p": 1,
            "q": 1,
            "distribution": "normal"
        }
    }
    """

    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)
        
        # Ensemble configuration
        config = self.freqai_info.get("model_training_parameters", {})
        self.ensemble_method = config.get("ensemble_method", "weighted_average")
        self.lstm_weight = config.get("lstm_weight", 0.4)
        self.prophet_weight = config.get("prophet_weight", 0.4)
        self.garch_weight = config.get("garch_weight", 0.2)
        self.use_meta_learner = config.get("use_meta_learner", True)
        self.meta_learner_type = config.get("meta_learner", "linear_regression")
        
        # Individual model configurations
        self.lstm_config = config.get("lstm_config", {})
        self.prophet_config = config.get("prophet_config", {})
        self.garch_config = config.get("garch_config", {})
        
        # Initialize models
        self.models = {}
        self.meta_learner = None
        self.scaler = StandardScaler()
        
        # Suppress warnings
        warnings.filterwarnings("ignore", category=FutureWarning)
        
    def fit(self, data_dictionary: Dict, dk: FreqaiDataKitchen, **kwargs) -> Any:
        """
        Train the ensemble of models.
        """
        
        logger.info("Training ensemble models...")
        
        # Train individual models
        self._train_individual_models(data_dictionary, dk)
        
        # Train meta-learner if enabled
        if self.use_meta_learner and len(self.models) > 1:
            self._train_meta_learner(data_dictionary, dk)
        
        logger.info(f"Ensemble training completed with {len(self.models)} models")
        
        return self

    def predict(
        self, unfiltered_df: DataFrame, dk: FreqaiDataKitchen, **kwargs
    ) -> Tuple[DataFrame, npt.NDArray[np.int_]]:
        """
        Make ensemble predictions.
        """
        
        # Get predictions from individual models
        model_predictions = {}
        outliers = np.array([])
        
        for model_name, model in self.models.items():
            try:
                pred_df, model_outliers = model.predict(unfiltered_df, dk, **kwargs)
                model_predictions[model_name] = pred_df.values
                
                # Combine outliers
                if len(outliers) == 0:
                    outliers = model_outliers
                else:
                    outliers = np.logical_or(outliers, model_outliers)
                    
            except Exception as e:
                logger.warning(f"Model {model_name} prediction failed: {e}")
                continue
        
        # Combine predictions
        if len(model_predictions) == 0:
            # Fallback prediction
            pred_df = DataFrame(
                np.zeros((len(unfiltered_df), 1)), 
                columns=[f"&-{dk.label_list[0]}"]
            )
            return pred_df, np.array([False] * len(unfiltered_df))
        
        # Ensemble prediction
        ensemble_pred = self._combine_predictions(model_predictions)
        
        # Create prediction dataframe
        pred_df = DataFrame(ensemble_pred, columns=[f"&-{dk.label_list[0]}"])
        
        return pred_df, outliers

    def _train_individual_models(self, data_dictionary: Dict, dk: FreqaiDataKitchen):
        """
        Train individual models in the ensemble.
        """
        
        # Train LSTM model
        if PYTORCH_AVAILABLE:
            try:
                logger.info("Training LSTM model...")
                lstm_config = self.freqai_info.copy()
                lstm_config["model_training_parameters"] = self.lstm_config
                
                lstm_model = PyTorchLSTMRegressor(config=self.config)
                lstm_model.fit(data_dictionary, dk)
                self.models["lstm"] = lstm_model
                logger.info("LSTM model trained successfully")
                
            except Exception as e:
                logger.warning(f"LSTM training failed: {e}")
        
        # Train Prophet model
        if PROPHET_AVAILABLE:
            try:
                logger.info("Training Prophet model...")
                prophet_config = self.freqai_info.copy()
                prophet_config["model_training_parameters"] = self.prophet_config
                
                prophet_model = ProphetRegressor(config=self.config)
                prophet_model.freqai_info = prophet_config
                prophet_model.fit(data_dictionary, dk)
                self.models["prophet"] = prophet_model
                logger.info("Prophet model trained successfully")
                
            except Exception as e:
                logger.warning(f"Prophet training failed: {e}")
        
        # Train GARCH model
        if GARCH_AVAILABLE:
            try:
                logger.info("Training GARCH model...")
                garch_config = self.freqai_info.copy()
                garch_config["model_training_parameters"] = self.garch_config
                
                garch_model = GARCHVolatilityModel(config=self.config)
                garch_model.freqai_info = garch_config
                garch_model.fit(data_dictionary, dk)
                self.models["garch"] = garch_model
                logger.info("GARCH model trained successfully")
                
            except Exception as e:
                logger.warning(f"GARCH training failed: {e}")

    def _train_meta_learner(self, data_dictionary: Dict, dk: FreqaiDataKitchen):
        """
        Train a meta-learner to optimally combine model predictions.
        """
        
        logger.info("Training meta-learner...")
        
        try:
            # Get predictions from individual models on training data
            model_preds = []
            
            for model_name, model in self.models.items():
                # Create temporary dataframe for prediction
                temp_df = data_dictionary["train_features"].copy()
                pred_df, _ = model.predict(temp_df, dk)
                model_preds.append(pred_df.values.flatten())
            
            if len(model_preds) < 2:
                logger.warning("Not enough models for meta-learner")
                return
            
            # Stack predictions
            X_meta = np.column_stack(model_preds)
            y_meta = data_dictionary["train_labels"].values.flatten()
            
            # Scale features
            X_meta_scaled = self.scaler.fit_transform(X_meta)
            
            # Train meta-learner
            if self.meta_learner_type == "linear_regression":
                self.meta_learner = LinearRegression()
            else:
                self.meta_learner = LinearRegression()  # Default fallback
            
            self.meta_learner.fit(X_meta_scaled, y_meta)
            
            logger.info("Meta-learner trained successfully")
            
        except Exception as e:
            logger.warning(f"Meta-learner training failed: {e}")
            self.meta_learner = None

    def _combine_predictions(self, model_predictions: Dict[str, np.ndarray]) -> np.ndarray:
        """
        Combine predictions from individual models.
        """
        
        if len(model_predictions) == 1:
            return list(model_predictions.values())[0]
        
        # Use meta-learner if available
        if self.meta_learner is not None:
            try:
                # Stack predictions
                pred_stack = np.column_stack(list(model_predictions.values()))
                pred_stack_scaled = self.scaler.transform(pred_stack)
                
                # Meta-learner prediction
                ensemble_pred = self.meta_learner.predict(pred_stack_scaled)
                return ensemble_pred.reshape(-1, 1)
                
            except Exception as e:
                logger.warning(f"Meta-learner prediction failed: {e}")
        
        # Fallback to weighted average
        weights = []
        predictions = []
        
        for model_name, pred in model_predictions.items():
            if model_name == "lstm":
                weights.append(self.lstm_weight)
            elif model_name == "prophet":
                weights.append(self.prophet_weight)
            elif model_name == "garch":
                weights.append(self.garch_weight)
            else:
                weights.append(1.0 / len(model_predictions))
            
            predictions.append(pred)
        
        # Normalize weights
        weights = np.array(weights)
        weights = weights / weights.sum()
        
        # Weighted average
        ensemble_pred = np.zeros_like(predictions[0])
        for i, pred in enumerate(predictions):
            ensemble_pred += weights[i] * pred
        
        return ensemble_pred
