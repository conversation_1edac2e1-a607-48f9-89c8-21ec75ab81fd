{
    "max_open_trades": 3,
    "stake_currency": "USDT",
    "stake_amount": "unlimited",
    "tradable_balance_ratio": 0.99,
    "fiat_display_currency": "USD",
    "dry_run": true,
    "dry_run_wallet": 1000,
    "cancel_open_orders_on_exit": false,
    "trading_mode": "futures",
    "margin_mode": "isolated",
    "unfilledtimeout": {
        "entry": 10,
        "exit": 10,
        "exit_timeout_count": 0,
        "unit": "minutes"
    },
    "entry_pricing": {
        "price_side": "other",
        "use_order_book": true,
        "order_book_top": 1,
        "price_last_balance": 0.0,
        "check_depth_of_market": {
            "enabled": false,
            "bids_to_ask_delta": 1
        }
    },
    "exit_pricing": {
        "price_side": "other",
        "use_order_book": true,
        "order_book_top": 1
    },
    "exchange": {
        "name": "gateio",
        "key": "",
        "secret": "",
        "ccxt_config": {},
        "ccxt_async_config": {},
        "pair_whitelist": [
            "ETH/USDT:USDT"
        ],
        "pair_blacklist": []
    },
    "pairlists": [
        {
            "method": "StaticPairList"
        }
    ],
    "strategy": "NeuralProphetFreqAIStrategy",
    "timeframe": "1h",
    "startup_candle_count": 200,
    "minimal_roi": {
        "0": 0.08,
        "120": 0.04,
        "240": 0.02,
        "480": 0.01
    },
    "stoploss": -0.03,
    "trailing_stop": false,
    "use_exit_signal": true,
    "exit_profit_only": false,
    "ignore_roi_if_entry_signal": false,
    "ignore_buying_expired_candle_after": 300,
    "order_types": {
        "entry": "market",
        "exit": "market",
        "emergency_exit": "market",
        "force_exit": "market",
        "force_entry": "market",
        "stoploss": "market",
        "stoploss_on_exchange": false,
        "stoploss_on_exchange_interval": 60
    },
    "order_time_in_force": {
        "entry": "GTC",
        "exit": "GTC"
    },
    "dataformat_ohlcv": "json",
    "dataformat_trades": "jsongz",
    "freqai": {
        "enabled": true,
        "model_save_type": "stable_baselines3",
        "conv_width": 2,
        "purge_old_models": 2,
        "train_period_days": 30,
        "backtest_period_days": 7,
        "live_retrain_hours": 0,
        "expiration_hours": 1,
        "save_backtest_models": true,
        "write_metrics_to_disk": true,
        "activate_tensorboard": true,
        "identifier": "neural_prophet_strategy",
        "feature_parameters": {
            "include_timeframes": [
                "5m",
                "15m",
                "1h"
            ],
            "include_shifted_candles": 2,
            "include_corr_pairlist": [
                "ETH/USDT:USDT",
                "HYPE/USDT:USDT"
            ],
            "label_period_candles": 24,
            "DI_threshold": 0.9,
            "weight_factor": 0.9,
            "principal_component_analysis": false,
            "use_SVM_to_remove_outliers": true,
            "svm_params": {
                "shuffle": false,
                "nu": 0.1
            },
            "use_DBSCAN_to_remove_outliers": false,
            "indicator_max_period_candles": 20,
            "indicator_periods_candles": [10, 20]
        },
        "data_split_parameters": {
            "test_size": 0.33,
            "shuffle": false
        },
        "model_training_parameters": {
            "n_estimators": 800,
            "learning_rate": 0.02,
            "max_depth": 8,
            "min_child_weight": 2,
            "subsample": 0.9,
            "colsample_bytree": 0.9,
            "random_state": 1
        },
    }
}
