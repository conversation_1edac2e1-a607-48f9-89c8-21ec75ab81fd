{"freqai": {"enabled": true, "model_save_type": "stable_baselines3", "conv_width": 2, "purge_old_models": 2, "train_period_days": 30, "backtest_period_days": 7, "live_retrain_hours": 0, "expiration_hours": 1, "save_backtest_models": true, "write_metrics_to_disk": true, "activate_tensorboard": true, "identifier": "neural_prophet_strategy", "feature_parameters": {"include_timeframes": ["5m", "15m", "1h"], "include_shifted_candles": 2, "include_corr_pairlist": ["ETH/USDT:USDT", "HYPE/USDT:USDT"], "label_period_candles": 24, "DI_threshold": 0.9, "weight_factor": 0.9, "principal_component_analysis": false, "use_SVM_to_remove_outliers": true, "svm_params": {"shuffle": false, "nu": 0.1}, "use_DBSCAN_to_remove_outliers": false, "indicator_max_period_candles": 20, "indicator_periods_candles": [10, 20], "plot_feature_importances": 0, "shuffle_after_split": false, "buffer_train_data_candles": 0}, "data_split_parameters": {"test_size": 0.33, "shuffle": false}, "model_training_parameters": {"n_estimators": 800, "learning_rate": 0.02, "max_depth": 8, "min_child_weight": 2, "subsample": 0.9, "colsample_bytree": 0.9, "random_state": 1}, "wait_for_training_iteration_on_reload": true, "continual_learning": false, "keras": false}, "timeframe": "5m", "stake_amount": "unlimited", "stake_currency": "USDT", "max_open_trades": 3, "pairs": ["ETH/USDT:USDT"]}