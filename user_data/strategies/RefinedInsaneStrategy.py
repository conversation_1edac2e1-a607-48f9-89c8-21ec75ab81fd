# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union
from functools import reduce

from freqtrade.strategy import (DecimalParameter, IntParameter, IStrategy)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
from technical import qtpylib


class RefinedInsaneStrategy(IStrategy):
    """
    Refined Insane Strategy - Bollinger Bands + RSI Strategy
    
    This strategy is based on the "01_Refined_Insane_Strategy.ipynb" notebook.
    It uses Bollinger Bands and RSI to identify oversold/overbought conditions
    and enters trades when price breaks out of these conditions.
    
    Entry Logic:
    - Long: Previous candle closes below BB lower band, RSI < 30, current candle closes above previous high
    - Short: Previous candle closes above BB upper band, RSI > 70, current candle closes below previous low
    - Both require BB width > threshold for volatility filter
    
    Exit Logic:
    - ATR-based stop loss and take profit
    """

    INTERFACE_VERSION = 3

    # Strategy interface version - allow new iterations of the strategy interface.
    # Check the documentation or the Sample strategy to get the latest version.

    # Optimal timeframe for the strategy.
    timeframe = '1d'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    # This attribute will be overridden if the config file contains "minimal_roi".
    minimal_roi = {
        "0": 0.50,   # 50% ROI
        "40": 0.25,  # 25% ROI after 40 minutes
        "120": 0.10, # 10% ROI after 2 hours
        "240": 0.05  # 5% ROI after 4 hours
    }

    # Optimal stoploss designed for the strategy.
    # This attribute will be overridden if the config file contains "stoploss".
    stoploss = -0.10

    # Trailing stoploss
    trailing_stop = False

    # Hyperopt parameters
    rsi_threshold_low = IntParameter(20, 40, default=30, space="buy")
    rsi_threshold_high = IntParameter(60, 80, default=70, space="sell")
    bb_width_threshold = DecimalParameter(0.0005, 0.005, default=0.001, space="buy")
    sl_coef = DecimalParameter(1.0, 3.0, default=1.5, space="sell")
    tp_coef = DecimalParameter(1.0, 3.0, default=1.4, space="sell")

    # Strategy parameters
    bb_period = 30
    bb_std = 2.0
    rsi_period = 14
    atr_period = 14

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 50

    # Optional order type mapping.
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }

    plot_config = {
        'main_plot': {
            'bb_lowerband': {'color': 'blue'},
            'bb_middleband': {'color': 'orange'},
            'bb_upperband': {'color': 'blue'},
        },
        'subplots': {
            "RSI": {
                'rsi': {'color': 'red'},
            },
            "BB_Width": {
                'bb_width': {'color': 'green'},
            }
        }
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        These pairs will automatically be available in the strategy.
        """
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame

        Performance Note: For the best performance be frugal on the number of indicators
        you are using. Let uncomment only the indicator you are using in your strategies
        or your hyperopt configuration, otherwise you will waste your memory and CPU usage.
        :param dataframe: Dataframe with data from the exchange
        :param metadata: Additional information, like the currently traded pair
        :return: a Dataframe with all mandatory indicators for the strategies
        """

        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(qtpylib.typical_price(dataframe), window=self.bb_period, stds=self.bb_std)
        dataframe['bb_lowerband'] = bollinger['lower']
        dataframe['bb_middleband'] = bollinger['mid']
        dataframe['bb_upperband'] = bollinger['upper']
        
        # Bollinger Bands Width (normalized by middle band)
        dataframe['bb_width'] = (dataframe['bb_upperband'] - dataframe['bb_lowerband']) / dataframe['bb_middleband']

        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period)

        # ATR
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period)

        # Previous candle values for comparison
        dataframe['prev_close'] = dataframe['close'].shift(1)
        dataframe['prev_high'] = dataframe['high'].shift(1)
        dataframe['prev_low'] = dataframe['low'].shift(1)
        dataframe['prev_rsi'] = dataframe['rsi'].shift(1)
        dataframe['prev_bb_lowerband'] = dataframe['bb_lowerband'].shift(1)
        dataframe['prev_bb_upperband'] = dataframe['bb_upperband'].shift(1)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with entry columns populated
        """
        
        # Long entry conditions (TotalSignal = 2 in original strategy)
        long_conditions = [
            # Previous candle closed below lower Bollinger Band
            (dataframe['prev_close'] < dataframe['prev_bb_lowerband']),
            # Previous candle RSI was oversold
            (dataframe['prev_rsi'] < self.rsi_threshold_low.value),
            # Current candle closes above previous candle's high (breakout)
            (dataframe['close'] > dataframe['prev_high']),
            # Bollinger Bands width is above threshold (volatility filter)
            (dataframe['bb_width'] > self.bb_width_threshold.value),
            # Volume check (optional)
            (dataframe['volume'] > 0)
        ]

        # Short entry conditions (TotalSignal = 1 in original strategy)
        short_conditions = [
            # Previous candle closed above upper Bollinger Band
            (dataframe['prev_close'] > dataframe['prev_bb_upperband']),
            # Previous candle RSI was overbought
            (dataframe['prev_rsi'] > self.rsi_threshold_high.value),
            # Current candle closes below previous candle's low (breakdown)
            (dataframe['close'] < dataframe['prev_low']),
            # Bollinger Bands width is above threshold (volatility filter)
            (dataframe['bb_width'] > self.bb_width_threshold.value),
            # Volume check (optional)
            (dataframe['volume'] > 0)
        ]

        # Combine all long conditions
        dataframe.loc[
            reduce(lambda x, y: x & y, long_conditions),
            'enter_long'] = 1

        # Combine all short conditions
        dataframe.loc[
            reduce(lambda x, y: x & y, short_conditions),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        :param dataframe: DataFrame
        :param metadata: Additional information, like the currently traded pair
        :return: DataFrame with exit columns populated
        """
        
        # Exit conditions can be based on opposite signals or specific exit rules
        # For this strategy, we'll rely mainly on stop loss and take profit
        # But we can add some basic exit conditions
        
        dataframe.loc[
            (
                # Exit long when RSI becomes very overbought
                (dataframe['rsi'] > 80) |
                # Or when price closes above upper Bollinger Band
                (dataframe['close'] > dataframe['bb_upperband'])
            ),
            'exit_long'] = 1

        dataframe.loc[
            (
                # Exit short when RSI becomes very oversold
                (dataframe['rsi'] < 20) |
                # Or when price closes below lower Bollinger Band
                (dataframe['close'] < dataframe['bb_lowerband'])
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss logic using ATR-based stops
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        if trade.is_short:
            # For short trades, stop loss is above entry
            sl_price = trade.open_rate + (last_candle['atr'] * self.sl_coef.value)
            return (sl_price - current_rate) / current_rate
        else:
            # For long trades, stop loss is below entry
            sl_price = trade.open_rate - (last_candle['atr'] * self.sl_coef.value)
            return (sl_price - current_rate) / current_rate

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        Custom exit logic using ATR-based take profit
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()
        
        if trade.is_short:
            # For short trades, take profit is below entry
            tp_price = trade.open_rate - (last_candle['atr'] * self.tp_coef.value)
            if current_rate <= tp_price:
                return 'atr_take_profit'
        else:
            # For long trades, take profit is above entry
            tp_price = trade.open_rate + (last_candle['atr'] * self.tp_coef.value)
            if current_rate >= tp_price:
                return 'atr_take_profit'
        
        return None
