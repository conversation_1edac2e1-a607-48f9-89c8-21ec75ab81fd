# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union
from functools import reduce

from freqtrade.strategy import (DecimalParameter, IntParameter, IStrategy)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
from technical import qtpylib


class OandaCandlestickStrategy(IStrategy):
    """
    OandaCandlestickStrategy - Candlestick Pattern Recognition Strategy

    This strategy is based on the "TradingBotOanda.ipynb" notebook.
    It identifies specific bullish and bearish candlestick patterns
    using two-candle combinations and executes trades with dynamic
    stop loss and take profit based on previous candle range.

    Core Pattern Logic:

    Bearish Pattern (Short Signal):
    - Current candle: Open > Close (bearish)
    - Previous candle: Open < Close (bullish)
    - Current close < Previous open (gap down or continuation)
    - Current open >= Previous close (overlap or gap)

    Bullish Pattern (Long Signal):
    - Current candle: Open < Close (bullish)
    - Previous candle: Open > Close (bearish)
    - Current close > Previous open (gap up or continuation)
    - Current open <= Previous close (overlap or gap)

    Risk Management:
    - Stop Loss: Previous candle range (High - Low)
    - Take Profit: 2x stop loss distance (configurable)
    """

    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = '15m'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.10,   # 10% ROI
        "30": 0.05,  # 5% ROI after 30 minutes
        "60": 0.02,  # 2% ROI after 1 hour
        "120": 0.01  # 1% ROI after 2 hours
    }

    # Optimal stoploss designed for the strategy.
    stoploss = -0.05

    # Trailing stoploss
    trailing_stop = False

    # Hyperopt parameters
    sltp_ratio = DecimalParameter(1.5, 3.0, default=2.0, space="sell")
    min_candle_range = DecimalParameter(0.0001, 0.001, default=0.0002, space="buy")
    volume_threshold = DecimalParameter(0.0, 2.0, default=0.0, space="buy")

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 10

    # Optional order type mapping.
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }

    plot_config = {
        'main_plot': {
            # No additional indicators on main plot
        },
        'subplots': {
            "Signals": {
                'signal': {'color': 'blue'},
                'candle_range': {'color': 'orange'},
            }
        }
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def signal_generator(self, dataframe: DataFrame, index: int) -> int:
        """
        Generate trading signals based on candlestick patterns

        Returns:
        0 = No signal
        1 = Bearish pattern (short)
        2 = Bullish pattern (long)
        """
        if index < 1:
            return 0

        # Current candle values
        current_open = dataframe['open'].iloc[index]
        current_close = dataframe['close'].iloc[index]

        # Previous candle values
        previous_open = dataframe['open'].iloc[index - 1]
        previous_close = dataframe['close'].iloc[index - 1]

        # Calculate previous candle range for minimum threshold
        previous_range = abs(dataframe['high'].iloc[index - 1] - dataframe['low'].iloc[index - 1])

        # Skip if previous candle range is too small (avoid noise)
        if previous_range < self.min_candle_range.value:
            return 0

        # Bearish Pattern
        if (current_open > current_close and           # Current candle is bearish
                previous_open < previous_close and         # Previous candle was bullish
                current_close < previous_open and          # Current close below previous open
                current_open >= previous_close):           # Current open at/above previous close
            return 1

        # Bullish Pattern
        elif (current_open < current_close and         # Current candle is bullish
              previous_open > previous_close and       # Previous candle was bearish
              current_close > previous_open and        # Current close above previous open
              current_open <= previous_close):         # Current open at/below previous close
            return 2

        # No clear pattern
        else:
            return 0

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds indicators to the given DataFrame
        """
        # Calculate signals for each candle
        dataframe['signal'] = 0
        for i in range(len(dataframe)):
            dataframe.loc[dataframe.index[i], 'signal'] = self.signal_generator(dataframe, i)

        # Calculate previous candle range for risk management
        dataframe['prev_candle_range'] = abs(dataframe['high'].shift(1) - dataframe['low'].shift(1))
        dataframe['candle_range'] = abs(dataframe['high'] - dataframe['low'])

        # Calculate dynamic stop loss and take profit levels
        dataframe['sl_long'] = dataframe['open'] - dataframe['prev_candle_range']
        dataframe['tp_long'] = dataframe['open'] + (dataframe['prev_candle_range'] * self.sltp_ratio.value)
        dataframe['sl_short'] = dataframe['open'] + dataframe['prev_candle_range']
        dataframe['tp_short'] = dataframe['open'] - (dataframe['prev_candle_range'] * self.sltp_ratio.value)

        # Add volume moving average for filtering
        dataframe['volume_ma'] = dataframe['volume'].rolling(window=20).mean()

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on candlestick patterns, populates the entry signal for the given dataframe
        """

        # Long entry conditions (Bullish Pattern - Signal = 2)
        long_conditions = [
            # Bullish candlestick pattern detected
            (dataframe['signal'] == 2),
            # Previous candle range is significant enough
            (dataframe['prev_candle_range'] >= self.min_candle_range.value),
            # Volume filter (optional)
            (dataframe['volume'] > dataframe['volume_ma'] * self.volume_threshold.value),
            # Basic data quality check
            (dataframe['volume'] > 0)
        ]

        # Short entry conditions (Bearish Pattern - Signal = 1)
        short_conditions = [
            # Bearish candlestick pattern detected
            (dataframe['signal'] == 1),
            # Previous candle range is significant enough
            (dataframe['prev_candle_range'] >= self.min_candle_range.value),
            # Volume filter (optional)
            (dataframe['volume'] > dataframe['volume_ma'] * self.volume_threshold.value),
            # Basic data quality check
            (dataframe['volume'] > 0)
        ]

        # Combine all long conditions
        dataframe.loc[
            reduce(lambda x, y: x & y, long_conditions),
            'enter_long'] = 1

        # Combine all short conditions
        dataframe.loc[
            reduce(lambda x, y: x & y, short_conditions),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on candlestick patterns, populates the exit signal for the given dataframe
        """

        # Exit long when bearish pattern appears
        dataframe.loc[
            (dataframe['signal'] == 1),
            'exit_long'] = 1

        # Exit short when bullish pattern appears
        dataframe.loc[
            (dataframe['signal'] == 2),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss logic based on previous candle range
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)

        # Use the last available candle for stop loss calculation
        if len(dataframe) < 2:
            return self.stoploss

        # Get the most recent candle's previous range
        last_candle = dataframe.iloc[-1]
        prev_candle_range = last_candle['prev_candle_range']

        # If no valid range data, use default stoploss
        if np.isnan(prev_candle_range) or prev_candle_range <= 0:
            return self.stoploss

        if trade.is_short:
            # For short trades, stop loss is above entry
            sl_price = trade.open_rate + prev_candle_range
            return (sl_price - current_rate) / current_rate
        else:
            # For long trades, stop loss is below entry
            sl_price = trade.open_rate - prev_candle_range
            return (sl_price - current_rate) / current_rate

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        Custom exit logic based on take profit levels
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)

        # Use the last available candle for take profit calculation
        if len(dataframe) < 2:
            return None

        # Get the most recent candle's previous range
        last_candle = dataframe.iloc[-1]
        prev_candle_range = last_candle['prev_candle_range']

        # If no valid range data, don't exit
        if np.isnan(prev_candle_range) or prev_candle_range <= 0:
            return None

        if trade.is_short:
            # For short trades, take profit is below entry
            tp_price = trade.open_rate - (prev_candle_range * self.sltp_ratio.value)
            if current_rate <= tp_price:
                return 'take_profit'
        else:
            # For long trades, take profit is above entry
            tp_price = trade.open_rate + (prev_candle_range * self.sltp_ratio.value)
            if current_rate >= tp_price:
                return 'take_profit'

        return None

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                            time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                            side: str, **kwargs) -> bool:
        """
        Called right before placing a buy/sell order.
        Can be used to cancel the trade entry.
        """
        # Get current market data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1]

        # Ensure we have a valid signal
        if side == "long" and last_candle['signal'] != 2:
            return False
        elif side == "short" and last_candle['signal'] != 1:
            return False

        # Ensure previous candle range is significant
        if last_candle['prev_candle_range'] < self.min_candle_range.value:
            return False

        return True
