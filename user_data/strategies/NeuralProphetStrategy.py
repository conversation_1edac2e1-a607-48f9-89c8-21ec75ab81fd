# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union
from functools import reduce
import copy
import warnings
warnings.filterwarnings('ignore')

from freqtrade.strategy import (DecimalParameter, IntParameter, IStrategy)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
from technical import qtpylib

# Prophet imports
try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False
    print("Warning: Prophet not available. Install with: pip install prophet")


class NeuralProphetStrategy(IStrategy):
    """
    NeuralProphetStrategy - Facebook Prophet-Based Forecasting Strategy

    This strategy is based on the "_Neural_Prophet_Indicator_EURUSD.ipynb" notebook.
    It uses Facebook Prophet time series forecasting to predict future price movements
    and generates trading signals based on the difference between predicted and current prices.

    Core Algorithm:
    1. Use historical price data to train Prophet model
    2. Generate forecast for next period(s)
    3. Compare forecast mean with current price
    4. Generate signals based on prediction difference threshold

    Signal Logic:
    - Signal 1 (Short): forecast.mean() - current_price < -diff_limit
    - Signal 2 (Long): forecast.mean() - current_price > +diff_limit
    - Signal 0 (Hold): prediction within diff_limit range

    Original Parameters:
    - backcandles: 100 (historical data for training)
    - frontpredictions: 1 (forecast 1 period ahead)
    - diff_limit: 0.005 (threshold for signal generation)
    """

    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = '1h'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.08,   # 8% ROI
        "120": 0.04, # 4% ROI after 2 hours
        "240": 0.02, # 2% ROI after 4 hours
        "480": 0.01  # 1% ROI after 8 hours
    }

    # Optimal stoploss designed for the strategy.
    stoploss = -0.03

    # Trailing stoploss
    trailing_stop = False

    # Hyperopt parameters
    backcandles = IntParameter(50, 200, default=100, space="buy")
    frontpredictions = IntParameter(1, 5, default=1, space="buy")
    diff_limit = DecimalParameter(0.001, 0.010, default=0.005, space="buy")
    min_periods_for_prediction = IntParameter(20, 100, default=50, space="buy")
    rsi_period = IntParameter(10, 30, default=14, space="sell")
    rsi_overbought = IntParameter(70, 90, default=80, space="sell")
    rsi_oversold = IntParameter(10, 30, default=20, space="sell")

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    # Optional order type mapping.
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }

    plot_config = {
        'main_plot': {
            'prophet_forecast': {'color': 'blue'},
            'prophet_upper': {'color': 'lightblue'},
            'prophet_lower': {'color': 'lightblue'},
        },
        'subplots': {
            "Prophet_Signal": {
                'prophet_signal': {'color': 'purple'},
            },
            "RSI": {
                'rsi': {'color': 'orange'},
            },
            "Forecast_Diff": {
                'forecast_diff': {'color': 'green'},
            }
        }
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def prophet_signal(self, df: DataFrame, index: int, backcandles: int,
                       frontpredictions: int, diff_limit: float) -> int:
        """
        Generate Prophet-based trading signal

        Args:
            df: DataFrame with OHLCV data
            index: Current index position
            backcandles: Number of historical candles for training
            frontpredictions: Number of periods to forecast
            diff_limit: Threshold for signal generation

        Returns:
            0: Hold, 1: Short signal, 2: Long signal
        """
        if not PROPHET_AVAILABLE:
            return 0

        if index < backcandles:
            return 0

        try:
            # Prepare data for Prophet (requires 'ds' and 'y' columns)
            dfsplit = df.iloc[index - backcandles:index + 1].copy()
            prophet_df = pd.DataFrame({
                'ds': dfsplit.index,
                'y': dfsplit['close']
            })

            # Initialize and fit Prophet model
            model = Prophet(
                daily_seasonality=False,
                weekly_seasonality=True,
                yearly_seasonality=False,
                changepoint_prior_scale=0.05,
                seasonality_prior_scale=10.0,
                holidays_prior_scale=10.0,
                seasonality_mode='multiplicative'
            )

            # Suppress Prophet's verbose output
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                model.fit(prophet_df)

            # Create future dataframe and make prediction
            future = model.make_future_dataframe(periods=frontpredictions, freq='H', include_history=False)
            forecast = model.predict(future)

            # Calculate signal based on forecast vs current price
            current_price = dfsplit['close'].iloc[-1]
            forecast_mean = forecast['yhat'].mean()
            price_diff = forecast_mean - current_price

            if price_diff < -diff_limit:
                return 1  # Short signal (price expected to fall)
            elif price_diff > diff_limit:
                return 2  # Long signal (price expected to rise)
            else:
                return 0  # Hold signal

        except Exception as e:
            # If Prophet fails, return hold signal
            return 0

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds Prophet forecasting indicators to the given DataFrame
        """
        if not PROPHET_AVAILABLE:
            dataframe['prophet_signal'] = 0
            dataframe['prophet_forecast'] = dataframe['close']
            dataframe['prophet_upper'] = dataframe['close']
            dataframe['prophet_lower'] = dataframe['close']
            dataframe['forecast_diff'] = 0
            dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
            return dataframe

        # Initialize Prophet signals
        prophet_signals = [0] * len(dataframe)
        prophet_forecasts = [np.nan] * len(dataframe)
        prophet_upper = [np.nan] * len(dataframe)
        prophet_lower = [np.nan] * len(dataframe)
        forecast_diffs = [0] * len(dataframe)

        # Generate Prophet signals for each valid index
        for i in range(self.backcandles.value, len(dataframe) - self.frontpredictions.value):
            try:
                signal = self.prophet_signal(
                    dataframe, i,
                    self.backcandles.value,
                    self.frontpredictions.value,
                    self.diff_limit.value
                )
                prophet_signals[i] = signal

                # Also get forecast values for plotting
                if i >= self.backcandles.value:
                    dfsplit = dataframe.iloc[i - self.backcandles.value:i + 1].copy()
                    prophet_df = pd.DataFrame({
                        'ds': dfsplit.index,
                        'y': dfsplit['close']
                    })

                    model = Prophet(
                        daily_seasonality=False,
                        weekly_seasonality=True,
                        yearly_seasonality=False,
                        changepoint_prior_scale=0.05
                    )

                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        model.fit(prophet_df)

                    future = model.make_future_dataframe(periods=self.frontpredictions.value, freq='H', include_history=False)
                    forecast = model.predict(future)

                    if len(forecast) > 0:
                        prophet_forecasts[i] = forecast['yhat'].iloc[0]
                        prophet_upper[i] = forecast['yhat_upper'].iloc[0]
                        prophet_lower[i] = forecast['yhat_lower'].iloc[0]
                        forecast_diffs[i] = forecast['yhat'].mean() - dfsplit['close'].iloc[-1]

            except Exception as e:
                prophet_signals[i] = 0
                continue

        # Add Prophet indicators to dataframe
        dataframe['prophet_signal'] = prophet_signals
        dataframe['prophet_forecast'] = prophet_forecasts
        dataframe['prophet_upper'] = prophet_upper
        dataframe['prophet_lower'] = prophet_lower
        dataframe['forecast_diff'] = forecast_diffs

        # Add RSI for additional confirmation
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)

        # Add ATR for volatility analysis
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on Prophet signals, populates the entry signal for the given dataframe
        """

        # Long entry conditions
        long_conditions = [
            # Prophet predicts price increase
            (dataframe['prophet_signal'] == 2),
            # RSI not overbought
            (dataframe['rsi'] < self.rsi_overbought.value),
            # Positive forecast difference
            (dataframe['forecast_diff'] > 0),
            # Volume check
            (dataframe['volume'] > 0)
        ]

        # Short entry conditions
        short_conditions = [
            # Prophet predicts price decrease
            (dataframe['prophet_signal'] == 1),
            # RSI not oversold
            (dataframe['rsi'] > self.rsi_oversold.value),
            # Negative forecast difference
            (dataframe['forecast_diff'] < 0),
            # Volume check
            (dataframe['volume'] > 0)
        ]

        # Apply conditions
        dataframe.loc[
            reduce(lambda x, y: x & y, long_conditions),
            'enter_long'] = 1

        dataframe.loc[
            reduce(lambda x, y: x & y, short_conditions),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on Prophet analysis, populates the exit signal for the given dataframe
        """

        # Exit long when Prophet predicts downward movement or RSI overbought
        dataframe.loc[
            (
                    (dataframe['prophet_signal'] == 1) |  # Prophet bearish
                    (dataframe['rsi'] > self.rsi_overbought.value) |  # RSI overbought
                    (dataframe['forecast_diff'] < -self.diff_limit.value)  # Strong negative forecast
            ),
            'exit_long'] = 1

        # Exit short when Prophet predicts upward movement or RSI oversold
        dataframe.loc[
            (
                    (dataframe['prophet_signal'] == 2) |  # Prophet bullish
                    (dataframe['rsi'] < self.rsi_oversold.value) |  # RSI oversold
                    (dataframe['forecast_diff'] > self.diff_limit.value)  # Strong positive forecast
            ),
            'exit_short'] = 1

        return dataframe

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                            time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                            side: str, **kwargs) -> bool:
        """
        Called right before placing a buy/sell order.
        Validates Prophet signal strength and market conditions.
        """
        if not PROPHET_AVAILABLE:
            return False

        # Get current market data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1]

        # Ensure we have a valid Prophet signal
        if last_candle['prophet_signal'] == 0:
            return False

        # Check forecast confidence (difference should be significant)
        if abs(last_candle['forecast_diff']) < self.diff_limit.value * 0.5:
            return False

        # Additional RSI confirmation
        if side == "long" and last_candle['rsi'] > self.rsi_overbought.value:
            return False
        if side == "short" and last_candle['rsi'] < self.rsi_oversold.value:
            return False

        return True

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        Customize leverage for Prophet-based trading
        """
        # Use conservative leverage for ML-based predictions
        return min(proposed_leverage, 2.0)
