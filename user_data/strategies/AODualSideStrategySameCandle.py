from freqtrade.strategy import IStrategy
from datetime import datetime
import pandas as pd
from typing import Optional
from freqtrade.persistence import Trade # Hinzugefügt für Type-Hinting

class AODualSideStrategySameCandle(IStrategy):
    INTERFACE_VERSION = 3

    timeframe = '1d'
    max_open_trades = 1

    order_types = {
        'entry': 'market',
        'exit': 'market',
        'emergency_exit': 'market',
        'force_exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    minimal_roi = {
        "0": 5.0  # 300% TP
    }
    stoploss = -0.8
    startup_candle_count: int = 1

    can_short = True
    position_adjustment_enable = False

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None, side: str,
                 **kwargs) -> float:
        return 2  # Fixed leverage

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        median_price = (dataframe['high'] + dataframe['low']) / 2
        dataframe['ao_fast'] = median_price.rolling(window=5).mean()
        dataframe['ao_slow'] = median_price.rolling(window=34).mean()
        dataframe['ao'] = dataframe['ao_fast'] - dataframe['ao_slow']
        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = None

        for i in range(len(dataframe)):
            ao_value = dataframe.at[i, 'ao']

            # Wenn AO > 0 → LONG, aber nur wenn kein anderer Trade bereits gesetzt
            if ao_value > 0:
                dataframe.at[i, 'enter_long'] = 1
                dataframe.at[i, 'enter_tag'] = 'ao_above_zero'
            # Wenn AO < 0 → SHORT, aber nur wenn LONG nicht bereits gesetzt
            elif ao_value < 0 and dataframe.at[i, 'enter_long'] == 0:
                dataframe.at[i, 'enter_short'] = 1
                dataframe.at[i, 'enter_tag'] = 'ao_below_zero'

        return dataframe

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        dataframe['exit_tag'] = None

        for i in range(len(dataframe)):
            ao_value = dataframe.at[i, 'ao']

            if ao_value < 0:
                dataframe.at[i, 'exit_long'] = 1
                dataframe.at[i, 'exit_tag'] = 'ao_below_zero_exit'
            elif ao_value > 0:
                dataframe.at[i, 'exit_short'] = 1
                dataframe.at[i, 'exit_tag'] = 'ao_above_zero_exit'

        return dataframe

    def custom_exit(self, pair: str, trade: Trade, current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs):
        # Take full profit at +500%
        if current_profit >= 5.00:
            return "tp_500_percent"

        return None

    # --- HINZUGEFÜGTER CODEBLOCK FÜR DAS LOGGING ---
    def confirm_trade_exit(self, pair: str, trade: Trade, order_type: str, amount: float,
                           rate: float, time_in_force: str, exit_reason: str,
                           current_time: datetime, **kwargs) -> bool:
        """
        Protokolliert die Details jedes geschlossenen Trades beim Backtesting.
        """
        profit_percent = trade.calc_profit_ratio(rate) * 100
        direction = trade.trade_direction
        # Ausgabe der gewünschten Informationen in der Konsole
        # Geändertes Log-Format: Datum vorne und im EU-Format (TT.MM.JJJJ HH:MM:SS)
        print(f"{current_time.strftime('%d.%m.%Y %H:%M:%S')} | Trade Closed: "
              f"Profit: {profit_percent:.2f}% | "
              f"Direction: {direction}")

        return True