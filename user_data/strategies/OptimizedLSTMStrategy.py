import logging
from functools import reduce
from typing import Dict
import numpy as np
import pandas as pd
import talib.abstract as ta
from pandas import DataFrame
from technical import qtpylib

from freqtrade.strategy import IStrategy, RealParameter, IntParameter, BooleanParameter

logger = logging.getLogger(__name__)


class OptimizedLSTMStrategy(IStrategy):
    """
    Optimized LSTM Strategy for ETH/USDT trading with advanced feature engineering.
    
    This strategy improves upon the basic LSTM approach with:
    - Enhanced technical indicators
    - Better risk management
    - Dynamic position sizing
    - Multi-timeframe analysis
    - Volatility-based adjustments
    """
    
    # Strategy parameters
    minimal_roi = {"0": 0.20, "30": 0.10, "60": 0.05, "120": 0.02, "240": 0}
    stoploss = -0.10
    trailing_stop = True
    trailing_stop_positive = 0.015
    trailing_stop_positive_offset = 0.025
    trailing_only_offset_is_reached = True
    
    timeframe = "5m"
    can_short = True
    use_exit_signal = True
    process_only_new_candles = True
    startup_candle_count = 200
    
    # Hyperopt parameters
    buy_threshold = RealParameter(-2.0, 2.0, default=0.5, space='buy')
    sell_threshold = RealParameter(-2.0, 2.0, default=-0.5, space='sell')
    
    # Signal strength and filters
    min_signal_strength = RealParameter(0.1, 1.5, default=0.4, space='buy')
    volatility_threshold = RealParameter(0.5, 3.0, default=1.2, space='buy')
    volume_threshold = RealParameter(0.8, 2.5, default=1.3, space='buy')
    
    # Risk management
    max_position_size = RealParameter(0.2, 1.0, default=0.6, space='buy')
    risk_multiplier = RealParameter(0.5, 2.0, default=1.0, space='buy')
    
    # Technical indicators
    rsi_period = IntParameter(10, 25, default=14, space='buy')
    rsi_overbought = IntParameter(70, 85, default=75, space='sell')
    rsi_oversold = IntParameter(15, 30, default=25, space='buy')
    
    ema_fast = IntParameter(8, 15, default=12, space='buy')
    ema_slow = IntParameter(20, 35, default=26, space='buy')
    
    bb_period = IntParameter(15, 25, default=20, space='buy')
    bb_std = RealParameter(1.8, 2.5, default=2.0, space='buy')
    
    # Advanced filters
    use_trend_filter = BooleanParameter(default=True, space='buy')
    use_volume_filter = BooleanParameter(default=True, space='buy')
    use_volatility_filter = BooleanParameter(default=True, space='buy')
    use_momentum_filter = BooleanParameter(default=True, space='buy')

    def feature_engineering_expand_all(
        self, dataframe: DataFrame, period: int, metadata: Dict, **kwargs
    ) -> DataFrame:
        """
        Enhanced feature engineering with comprehensive technical indicators.
        """
        
        # Core oscillators
        dataframe[f"%-rsi-{period}"] = ta.RSI(dataframe, timeperiod=period)
        dataframe[f"%-mfi-{period}"] = ta.MFI(dataframe, timeperiod=period)
        dataframe[f"%-cci-{period}"] = ta.CCI(dataframe, timeperiod=period)
        dataframe[f"%-williams-{period}"] = ta.WILLR(dataframe, timeperiod=period)
        dataframe[f"%-stoch-{period}"] = ta.STOCH(dataframe, timeperiod=period)['slowk']
        dataframe[f"%-stochrsi-{period}"] = ta.STOCHRSI(dataframe, timeperiod=period)['fastk']
        
        # Trend indicators
        dataframe[f"%-adx-{period}"] = ta.ADX(dataframe, timeperiod=period)
        dataframe[f"%-aroon_up-{period}"] = ta.AROON(dataframe, timeperiod=period)['aroonup']
        dataframe[f"%-aroon_down-{period}"] = ta.AROON(dataframe, timeperiod=period)['aroondown']
        dataframe[f"%-dx-{period}"] = ta.DX(dataframe, timeperiod=period)
        
        # Moving averages
        dataframe[f"%-sma-{period}"] = ta.SMA(dataframe, timeperiod=period)
        dataframe[f"%-ema-{period}"] = ta.EMA(dataframe, timeperiod=period)
        dataframe[f"%-wma-{period}"] = ta.WMA(dataframe, timeperiod=period)
        dataframe[f"%-tema-{period}"] = ta.TEMA(dataframe, timeperiod=period)
        dataframe[f"%-kama-{period}"] = ta.KAMA(dataframe, timeperiod=period)
        dataframe[f"%-mama-{period}"] = ta.MAMA(dataframe)['mama']
        
        # MACD variations
        macd, macdsignal, macdhist = ta.MACD(dataframe, fastperiod=12, slowperiod=26, signalperiod=9)
        dataframe[f"%-macd-{period}"] = macd
        dataframe[f"%-macdsignal-{period}"] = macdsignal
        dataframe[f"%-macdhist-{period}"] = macdhist
        dataframe[f"%-macdext-{period}"] = ta.MACDEXT(dataframe)['macd']
        
        # Bollinger Bands and variants
        bollinger = qtpylib.bollinger_bands(
            qtpylib.typical_price(dataframe), window=period, stds=2.0
        )
        dataframe[f"bb_lowerband-{period}"] = bollinger["lower"]
        dataframe[f"bb_middleband-{period}"] = bollinger["mid"]
        dataframe[f"bb_upperband-{period}"] = bollinger["upper"]
        dataframe[f"%-bb_width-{period}"] = (
            dataframe[f"bb_upperband-{period}"] - dataframe[f"bb_lowerband-{period}"]
        ) / dataframe[f"bb_middleband-{period}"]
        dataframe[f"%-bb_position-{period}"] = (
            dataframe["close"] - dataframe[f"bb_lowerband-{period}"]
        ) / (dataframe[f"bb_upperband-{period}"] - dataframe[f"bb_lowerband-{period}"])
        
        # Momentum indicators
        dataframe[f"%-roc-{period}"] = ta.ROC(dataframe, timeperiod=period)
        dataframe[f"%-mom-{period}"] = ta.MOM(dataframe, timeperiod=period)
        dataframe[f"%-cmo-{period}"] = ta.CMO(dataframe, timeperiod=period)
        dataframe[f"%-ppo-{period}"] = ta.PPO(dataframe, fastperiod=12, slowperiod=26)
        dataframe[f"%-trix-{period}"] = ta.TRIX(dataframe, timeperiod=period)
        
        # Volatility indicators
        dataframe[f"%-atr-{period}"] = ta.ATR(dataframe, timeperiod=period)
        dataframe[f"%-natr-{period}"] = ta.NATR(dataframe, timeperiod=period)
        dataframe[f"%-trange-{period}"] = ta.TRANGE(dataframe)
        
        # Volume indicators
        dataframe[f"%-ad-{period}"] = ta.AD(dataframe)
        dataframe[f"%-adosc-{period}"] = ta.ADOSC(dataframe, fastperiod=3, slowperiod=10)
        dataframe[f"%-obv-{period}"] = ta.OBV(dataframe)
        dataframe[f"%-volume_sma-{period}"] = dataframe["volume"].rolling(period).mean()
        dataframe[f"%-relative_volume-{period}"] = (
            dataframe["volume"] / dataframe[f"%-volume_sma-{period}"]
        )
        
        # Price patterns
        dataframe[f"%-hl2-{period}"] = (dataframe["high"] + dataframe["low"]) / 2
        dataframe[f"%-hlc3-{period}"] = (dataframe["high"] + dataframe["low"] + dataframe["close"]) / 3
        dataframe[f"%-ohlc4-{period}"] = (dataframe["open"] + dataframe["high"] + dataframe["low"] + dataframe["close"]) / 4
        
        return dataframe

    def feature_engineering_expand_basic(
        self, dataframe: DataFrame, metadata: Dict, **kwargs
    ) -> DataFrame:
        """
        Basic features for price action and volume analysis.
        """
        
        # Price features
        dataframe["%-pct_change"] = dataframe["close"].pct_change()
        dataframe["%-log_return"] = np.log(dataframe["close"] / dataframe["close"].shift(1))
        dataframe["%-price_position"] = dataframe["close"] / dataframe["high"]
        dataframe["%-hl_ratio"] = (dataframe["high"] - dataframe["low"]) / dataframe["close"]
        
        # Volume features
        dataframe["%-raw_volume"] = dataframe["volume"]
        dataframe["%-volume_price"] = dataframe["volume"] * dataframe["close"]
        dataframe["%-vwap"] = qtpylib.vwap(dataframe)
        
        # Candlestick features
        dataframe["%-body_size"] = abs(dataframe["close"] - dataframe["open"]) / dataframe["close"]
        dataframe["%-upper_shadow"] = (dataframe["high"] - np.maximum(dataframe["close"], dataframe["open"])) / dataframe["close"]
        dataframe["%-lower_shadow"] = (np.minimum(dataframe["close"], dataframe["open"]) - dataframe["low"]) / dataframe["close"]
        dataframe["%-is_green"] = (dataframe["close"] > dataframe["open"]).astype(int)
        
        # Gap analysis
        dataframe["%-gap"] = (dataframe["open"] - dataframe["close"].shift(1)) / dataframe["close"].shift(1)
        
        return dataframe

    def feature_engineering_standard(
        self, dataframe: DataFrame, metadata: Dict, **kwargs
    ) -> DataFrame:
        """
        Standard features including time-based patterns.
        """
        
        # Time features
        dataframe["%-day_of_week"] = dataframe["date"].dt.dayofweek
        dataframe["%-hour_of_day"] = dataframe["date"].dt.hour
        dataframe["%-minute_of_hour"] = dataframe["date"].dt.minute
        dataframe["%-is_weekend"] = (dataframe["date"].dt.dayofweek >= 5).astype(int)
        
        # Market sessions (crypto is 24/7 but still has patterns)
        dataframe["%-asian_session"] = ((dataframe["date"].dt.hour >= 0) & (dataframe["date"].dt.hour < 8)).astype(int)
        dataframe["%-european_session"] = ((dataframe["date"].dt.hour >= 8) & (dataframe["date"].dt.hour < 16)).astype(int)
        dataframe["%-american_session"] = ((dataframe["date"].dt.hour >= 16) & (dataframe["date"].dt.hour < 24)).astype(int)
        
        return dataframe

    def set_freqai_targets(self, dataframe: DataFrame, metadata: Dict, **kwargs) -> DataFrame:
        """
        Enhanced target engineering with multiple prediction horizons.
        """
        
        label_period = self.freqai_info["feature_parameters"]["label_period_candles"]
        
        # Price change targets
        dataframe["&-price_change_1"] = (
            dataframe["close"].shift(-1) / dataframe["close"] - 1
        )
        
        dataframe["&-price_change_short"] = (
            dataframe["close"].shift(-label_period//3) / dataframe["close"] - 1
        )
        
        dataframe["&-price_change_medium"] = (
            dataframe["close"].shift(-label_period//2) / dataframe["close"] - 1
        )
        
        dataframe["&-price_change_long"] = (
            dataframe["close"].shift(-label_period) / dataframe["close"] - 1
        )
        
        # Volatility and trend targets
        dataframe["&-volatility"] = (
            dataframe["close"].rolling(label_period).std() / dataframe["close"]
        ).shift(-label_period)
        
        dataframe["&-trend_strength"] = (
            dataframe["close"].rolling(label_period).apply(
                lambda x: np.corrcoef(np.arange(len(x)), x)[0, 1] if len(x) > 1 else 0
            )
        ).shift(-label_period)
        
        # Enhanced composite target with dynamic weighting
        volatility_weight = np.clip(dataframe["&-volatility"].rolling(50).mean(), 0.05, 0.25)
        trend_weight = np.clip(abs(dataframe["&-trend_strength"].rolling(50).mean()), 0.05, 0.25)
        
        dataframe["&-target"] = (
            0.4 * dataframe["&-price_change_long"] +
            0.3 * dataframe["&-price_change_medium"] +
            0.2 * dataframe["&-price_change_short"] +
            volatility_weight * dataframe["&-volatility"] +
            trend_weight * dataframe["&-trend_strength"]
        )
        
        return dataframe

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populate indicators and start FreqAI.
        """

        # Start FreqAI
        dataframe = self.freqai.start(dataframe, metadata, self)

        # Additional indicators for entry/exit logic
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=self.ema_fast.value)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=self.ema_slow.value)

        # Bollinger Bands
        bb = qtpylib.bollinger_bands(dataframe['close'], window=self.bb_period.value, stds=self.bb_std.value)
        dataframe['bb_lower'] = bb['lower']
        dataframe['bb_middle'] = bb['mid']
        dataframe['bb_upper'] = bb['upper']
        dataframe['bb_width'] = (dataframe['bb_upper'] - dataframe['bb_lower']) / dataframe['bb_middle']
        dataframe['bb_position'] = (dataframe['close'] - dataframe['bb_lower']) / (dataframe['bb_upper'] - dataframe['bb_lower'])

        # Volume analysis
        dataframe['volume_sma'] = dataframe['volume'].rolling(20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']

        # Volatility measures
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['atr_ratio'] = dataframe['atr'] / dataframe['close']

        # Momentum indicators
        dataframe['adx'] = ta.ADX(dataframe, timeperiod=14)
        dataframe['macd'], dataframe['macd_signal'], dataframe['macd_hist'] = ta.MACD(dataframe)

        # Support/Resistance levels
        dataframe['support'] = dataframe['low'].rolling(20).min()
        dataframe['resistance'] = dataframe['high'].rolling(20).max()
        dataframe['support_distance'] = (dataframe['close'] - dataframe['support']) / dataframe['close']
        dataframe['resistance_distance'] = (dataframe['resistance'] - dataframe['close']) / dataframe['close']

        return dataframe

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        Enhanced entry logic with multiple confirmation signals.
        """

        # ML prediction conditions
        ml_long_signal = df["&-target"] > self.buy_threshold.value
        ml_short_signal = df["&-target"] < self.sell_threshold.value

        # Signal strength filter
        signal_strength = abs(df["&-target"])
        strong_signal = signal_strength > self.min_signal_strength.value

        # Trend conditions
        trend_up = df['ema_fast'] > df['ema_slow']
        trend_down = df['ema_fast'] < df['ema_slow']
        strong_trend = df['adx'] > 25

        # RSI conditions
        rsi_not_overbought = df['rsi'] < self.rsi_overbought.value
        rsi_not_oversold = df['rsi'] > self.rsi_oversold.value
        rsi_bullish = df['rsi'] > 50
        rsi_bearish = df['rsi'] < 50

        # Volume conditions
        volume_condition = True
        if self.use_volume_filter.value:
            volume_condition = df['volume_ratio'] > self.volume_threshold.value

        # Volatility conditions
        volatility_condition = True
        if self.use_volatility_filter.value:
            volatility_condition = df['atr_ratio'] < self.volatility_threshold.value

        # Momentum conditions
        momentum_condition_long = True
        momentum_condition_short = True
        if self.use_momentum_filter.value:
            momentum_condition_long = df['macd'] > df['macd_signal']
            momentum_condition_short = df['macd'] < df['macd_signal']

        # Trend filter
        trend_condition_long = True
        trend_condition_short = True
        if self.use_trend_filter.value:
            trend_condition_long = trend_up & strong_trend
            trend_condition_short = trend_down & strong_trend

        # Bollinger Band conditions
        bb_long_condition = df['bb_position'] < 0.8  # Not too close to upper band
        bb_short_condition = df['bb_position'] > 0.2  # Not too close to lower band

        # Support/Resistance conditions
        near_support = df['support_distance'] < 0.02
        near_resistance = df['resistance_distance'] < 0.02

        # Long entry conditions
        enter_long_conditions = [
            df["do_predict"] == 1,
            ml_long_signal,
            strong_signal,
            trend_condition_long,
            rsi_not_overbought,
            volume_condition,
            volatility_condition,
            momentum_condition_long,
            bb_long_condition,
            ~near_resistance,  # Not near resistance
            df['volume'] > 0
        ]

        # Short entry conditions
        enter_short_conditions = [
            df["do_predict"] == 1,
            ml_short_signal,
            strong_signal,
            trend_condition_short,
            rsi_not_oversold,
            volume_condition,
            volatility_condition,
            momentum_condition_short,
            bb_short_condition,
            ~near_support,  # Not near support
            df['volume'] > 0
        ]

        # Apply conditions
        if enter_long_conditions:
            df.loc[
                reduce(lambda x, y: x & y, enter_long_conditions),
                ["enter_long", "enter_tag"]
            ] = (1, "lstm_long")

        if enter_short_conditions:
            df.loc[
                reduce(lambda x, y: x & y, enter_short_conditions),
                ["enter_short", "enter_tag"]
            ] = (1, "lstm_short")

        return df

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        Enhanced exit logic with multiple exit strategies.
        """

        # ML-based exit signals
        ml_exit_long = df["&-target"] < -self.min_signal_strength.value
        ml_exit_short = df["&-target"] > self.min_signal_strength.value

        # Technical exit conditions
        rsi_extreme_high = df['rsi'] > 85
        rsi_extreme_low = df['rsi'] < 15

        # Trend reversal signals
        trend_reversal_down = (df['ema_fast'] < df['ema_slow']) & (df['ema_fast'].shift(1) >= df['ema_slow'].shift(1))
        trend_reversal_up = (df['ema_fast'] > df['ema_slow']) & (df['ema_fast'].shift(1) <= df['ema_slow'].shift(1))

        # MACD divergence
        macd_bearish = (df['macd'] < df['macd_signal']) & (df['macd'].shift(1) >= df['macd_signal'].shift(1))
        macd_bullish = (df['macd'] > df['macd_signal']) & (df['macd'].shift(1) <= df['macd_signal'].shift(1))

        # Bollinger Band exits
        bb_exit_long = df['bb_position'] > 0.95
        bb_exit_short = df['bb_position'] < 0.05

        # Support/Resistance hits
        hit_resistance = df['resistance_distance'] < 0.005
        hit_support = df['support_distance'] < 0.005

        # Volatility spike (potential reversal)
        volatility_spike = df['atr_ratio'] > df['atr_ratio'].rolling(20).mean() * 2

        # Long exit conditions
        exit_long_conditions = [
            df["do_predict"] == 1,
            (ml_exit_long | rsi_extreme_high | trend_reversal_down |
             macd_bearish | bb_exit_long | hit_resistance | volatility_spike)
        ]

        # Short exit conditions
        exit_short_conditions = [
            df["do_predict"] == 1,
            (ml_exit_short | rsi_extreme_low | trend_reversal_up |
             macd_bullish | bb_exit_short | hit_support | volatility_spike)
        ]

        # Apply exit conditions
        if exit_long_conditions:
            df.loc[
                reduce(lambda x, y: x & y, exit_long_conditions),
                ["exit_long", "exit_tag"]
            ] = (1, "lstm_exit_long")

        if exit_short_conditions:
            df.loc[
                reduce(lambda x, y: x & y, exit_short_conditions),
                ["exit_short", "exit_tag"]
            ] = (1, "lstm_exit_short")

        return df

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time,
        entry_tag,
        side: str,
        **kwargs,
    ) -> bool:
        """
        Additional trade confirmation with enhanced risk management.
        """

        df, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = df.iloc[-1].squeeze()

        # Price deviation check (tighter for better execution)
        max_deviation = 0.003  # 0.3%

        if side == "long":
            if rate > (last_candle["close"] * (1 + max_deviation)):
                return False
        else:
            if rate < (last_candle["close"] * (1 - max_deviation)):
                return False

        # Enhanced signal strength check
        signal_strength = abs(last_candle.get("&-target", 0))
        if signal_strength < self.min_signal_strength.value:
            return False

        # Volume confirmation
        volume_ratio = last_candle.get("volume_ratio", 0)
        if volume_ratio < 0.8:  # Minimum volume requirement
            return False

        # Volatility check (avoid entering during extreme volatility)
        atr_ratio = last_candle.get("atr_ratio", 0)
        if atr_ratio > self.volatility_threshold.value:
            return False

        # RSI extremes check
        rsi = last_candle.get("rsi", 50)
        if side == "long" and rsi > 80:
            return False
        if side == "short" and rsi < 20:
            return False

        return True

    def custom_stake_amount(
        self,
        pair: str,
        current_time,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag,
        side: str,
        **kwargs,
    ) -> float:
        """
        Dynamic position sizing based on signal strength, volatility, and risk.
        """

        df, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = df.iloc[-1].squeeze()

        # Base stake
        base_stake = proposed_stake * self.max_position_size.value

        # Signal strength multiplier (stronger signals get larger positions)
        signal_strength = abs(last_candle.get("&-target", 0))
        strength_multiplier = min(signal_strength * 1.5, 1.2)

        # Volatility adjustment (reduce size in high volatility)
        volatility = last_candle.get("atr_ratio", 0.02)
        volatility_multiplier = max(0.4, 1.2 - volatility)

        # Trend strength adjustment
        adx = last_candle.get("adx", 25)
        trend_multiplier = min(adx / 25, 1.3) if adx > 25 else 0.8

        # Volume confirmation adjustment
        volume_ratio = last_candle.get("volume_ratio", 1.0)
        volume_multiplier = min(volume_ratio / 1.5, 1.2) if volume_ratio > 1.0 else 0.9

        # Calculate final stake
        final_stake = (base_stake * strength_multiplier * volatility_multiplier *
                      trend_multiplier * volume_multiplier * self.risk_multiplier.value)

        # Ensure within bounds
        final_stake = max(min_stake, min(final_stake, max_stake))

        return final_stake
