import logging
from functools import reduce
from typing import Dict
import numpy as np
import pandas as pd
import talib.abstract as ta
from pandas import DataFrame
from technical import qtpylib

from freqtrade.strategy import IStrategy, RealParameter, IntParameter, BooleanParameter

logger = logging.getLogger(__name__)


class AdvancedMLStrategy(IStrategy):
    """
    Advanced Machine Learning Strategy combining LSTM, Prophet, and GARCH models.
    
    This strategy uses an ensemble of machine learning models to predict price movements:
    - LSTM for sequential pattern recognition
    - Prophet for trend and seasonality analysis  
    - GARCH for volatility modeling
    - Comprehensive technical indicators for feature engineering
    
    Optimized for ETH/USDT on 5m and 15m timeframes.
    """
    
    # Strategy parameters
    minimal_roi = {"0": 0.15, "60": 0.05, "120": 0.02, "240": 0}
    stoploss = -0.08
    trailing_stop = True
    trailing_stop_positive = 0.01
    trailing_stop_positive_offset = 0.02
    trailing_only_offset_is_reached = True
    
    timeframe = "15m"
    can_short = True
    use_exit_signal = True
    process_only_new_candles = True
    startup_candle_count = 200
    
    # Hyperopt parameters
    buy_threshold = RealParameter(-1.0, 1.0, default=0.1, space='buy')
    sell_threshold = RealParameter(-1.0, 1.0, default=-0.1, space='sell')
    
    # Signal strength parameters
    min_signal_strength = RealParameter(0.1, 0.9, default=0.3, space='buy')
    volatility_threshold = RealParameter(0.1, 2.0, default=0.5, space='buy')
    
    # Risk management parameters
    max_position_size = RealParameter(0.1, 1.0, default=0.5, space='buy')
    risk_reward_ratio = RealParameter(1.0, 5.0, default=2.0, space='buy')
    
    # Technical indicator parameters
    rsi_period = IntParameter(10, 30, default=14, space='buy')
    ema_fast = IntParameter(8, 20, default=12, space='buy')
    ema_slow = IntParameter(20, 50, default=26, space='buy')
    bb_period = IntParameter(15, 30, default=20, space='buy')
    
    # Advanced parameters
    use_volume_filter = BooleanParameter(default=True, space='buy')
    use_volatility_filter = BooleanParameter(default=True, space='buy')
    use_trend_filter = BooleanParameter(default=True, space='buy')

    def feature_engineering_expand_all(
        self, dataframe: DataFrame, period: int, metadata: Dict, **kwargs
    ) -> DataFrame:
        """
        Comprehensive feature engineering with all useful technical indicators.
        """
        
        # Price-based indicators
        dataframe[f"%-rsi-{period}"] = ta.RSI(dataframe, timeperiod=period)
        dataframe[f"%-mfi-{period}"] = ta.MFI(dataframe, timeperiod=period)
        dataframe[f"%-adx-{period}"] = ta.ADX(dataframe, timeperiod=period)
        dataframe[f"%-cci-{period}"] = ta.CCI(dataframe, timeperiod=period)
        dataframe[f"%-williams-{period}"] = ta.WILLR(dataframe, timeperiod=period)
        dataframe[f"%-stoch-{period}"] = ta.STOCH(dataframe, timeperiod=period)['slowk']
        dataframe[f"%-stochrsi-{period}"] = ta.STOCHRSI(dataframe, timeperiod=period)['fastk']
        
        # Moving averages
        dataframe[f"%-sma-{period}"] = ta.SMA(dataframe, timeperiod=period)
        dataframe[f"%-ema-{period}"] = ta.EMA(dataframe, timeperiod=period)
        dataframe[f"%-wma-{period}"] = ta.WMA(dataframe, timeperiod=period)
        dataframe[f"%-tema-{period}"] = ta.TEMA(dataframe, timeperiod=period)
        dataframe[f"%-kama-{period}"] = ta.KAMA(dataframe, timeperiod=period)
        
        # MACD family
        macd, macdsignal, macdhist = ta.MACD(dataframe, fastperiod=12, slowperiod=26, signalperiod=9)
        dataframe[f"%-macd-{period}"] = macd
        dataframe[f"%-macdsignal-{period}"] = macdsignal
        dataframe[f"%-macdhist-{period}"] = macdhist
        
        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(
            qtpylib.typical_price(dataframe), window=period, stds=2.0
        )
        dataframe[f"bb_lowerband-{period}"] = bollinger["lower"]
        dataframe[f"bb_middleband-{period}"] = bollinger["mid"]
        dataframe[f"bb_upperband-{period}"] = bollinger["upper"]
        dataframe[f"%-bb_width-{period}"] = (
            dataframe[f"bb_upperband-{period}"] - dataframe[f"bb_lowerband-{period}"]
        ) / dataframe[f"bb_middleband-{period}"]
        dataframe[f"%-bb_position-{period}"] = (
            dataframe["close"] - dataframe[f"bb_lowerband-{period}"]
        ) / (dataframe[f"bb_upperband-{period}"] - dataframe[f"bb_lowerband-{period}"])
        
        # Momentum indicators
        dataframe[f"%-roc-{period}"] = ta.ROC(dataframe, timeperiod=period)
        dataframe[f"%-mom-{period}"] = ta.MOM(dataframe, timeperiod=period)
        dataframe[f"%-cmo-{period}"] = ta.CMO(dataframe, timeperiod=period)
        dataframe[f"%-ppo-{period}"] = ta.PPO(dataframe, fastperiod=12, slowperiod=26)
        
        # Volatility indicators
        dataframe[f"%-atr-{period}"] = ta.ATR(dataframe, timeperiod=period)
        dataframe[f"%-natr-{period}"] = ta.NATR(dataframe, timeperiod=period)
        dataframe[f"%-trange-{period}"] = ta.TRANGE(dataframe)
        
        # Volume indicators
        dataframe[f"%-ad-{period}"] = ta.AD(dataframe)
        dataframe[f"%-adosc-{period}"] = ta.ADOSC(dataframe, fastperiod=3, slowperiod=10)
        dataframe[f"%-obv-{period}"] = ta.OBV(dataframe)
        dataframe[f"%-volume_sma-{period}"] = dataframe["volume"].rolling(period).mean()
        dataframe[f"%-relative_volume-{period}"] = (
            dataframe["volume"] / dataframe[f"%-volume_sma-{period}"]
        )
        
        # Pattern recognition (selected patterns)
        dataframe[f"%-cdl_doji-{period}"] = ta.CDLDOJI(dataframe)
        dataframe[f"%-cdl_hammer-{period}"] = ta.CDLHAMMER(dataframe)
        dataframe[f"%-cdl_engulfing-{period}"] = ta.CDLENGULFING(dataframe)
        dataframe[f"%-cdl_harami-{period}"] = ta.CDLHARAMI(dataframe)
        
        return dataframe

    def feature_engineering_expand_basic(
        self, dataframe: DataFrame, metadata: Dict, **kwargs
    ) -> DataFrame:
        """
        Basic features that don't need period expansion.
        """
        
        # Price features
        dataframe["%-pct_change"] = dataframe["close"].pct_change()
        dataframe["%-log_return"] = np.log(dataframe["close"] / dataframe["close"].shift(1))
        dataframe["%-price_position"] = dataframe["close"] / dataframe["high"]
        dataframe["%-hl_ratio"] = (dataframe["high"] - dataframe["low"]) / dataframe["close"]
        
        # Volume features
        dataframe["%-raw_volume"] = dataframe["volume"]
        dataframe["%-volume_price"] = dataframe["volume"] * dataframe["close"]
        dataframe["%-vwap"] = qtpylib.vwap(dataframe)
        
        # Price action features
        dataframe["%-body_size"] = abs(dataframe["close"] - dataframe["open"]) / dataframe["close"]
        dataframe["%-upper_shadow"] = (dataframe["high"] - np.maximum(dataframe["close"], dataframe["open"])) / dataframe["close"]
        dataframe["%-lower_shadow"] = (np.minimum(dataframe["close"], dataframe["open"]) - dataframe["low"]) / dataframe["close"]
        
        return dataframe

    def feature_engineering_standard(
        self, dataframe: DataFrame, metadata: Dict, **kwargs
    ) -> DataFrame:
        """
        Standard features for time-based patterns.
        """
        
        # Time-based features
        dataframe["%-day_of_week"] = dataframe["date"].dt.dayofweek
        dataframe["%-hour_of_day"] = dataframe["date"].dt.hour
        dataframe["%-minute_of_hour"] = dataframe["date"].dt.minute
        dataframe["%-is_weekend"] = (dataframe["date"].dt.dayofweek >= 5).astype(int)
        
        # Market session features (assuming UTC)
        dataframe["%-asian_session"] = ((dataframe["date"].dt.hour >= 0) & (dataframe["date"].dt.hour < 8)).astype(int)
        dataframe["%-european_session"] = ((dataframe["date"].dt.hour >= 8) & (dataframe["date"].dt.hour < 16)).astype(int)
        dataframe["%-american_session"] = ((dataframe["date"].dt.hour >= 16) & (dataframe["date"].dt.hour < 24)).astype(int)
        
        return dataframe

    def set_freqai_targets(self, dataframe: DataFrame, metadata: Dict, **kwargs) -> DataFrame:
        """
        Set sophisticated targets for ML prediction.
        """
        
        # Future price change (main target)
        label_period = self.freqai_info["feature_parameters"]["label_period_candles"]
        
        # Multi-horizon targets
        dataframe["&-price_change_1"] = (
            dataframe["close"].shift(-1) / dataframe["close"] - 1
        )
        
        dataframe["&-price_change_short"] = (
            dataframe["close"].shift(-label_period//2) / dataframe["close"] - 1
        )
        
        dataframe["&-price_change_long"] = (
            dataframe["close"].shift(-label_period) / dataframe["close"] - 1
        )
        
        # Volatility target
        dataframe["&-volatility"] = (
            dataframe["close"].rolling(label_period).std() / dataframe["close"]
        ).shift(-label_period)
        
        # Trend strength target
        dataframe["&-trend_strength"] = (
            dataframe["close"].rolling(label_period).apply(
                lambda x: np.corrcoef(np.arange(len(x)), x)[0, 1] if len(x) > 1 else 0
            )
        ).shift(-label_period)
        
        # Main ensemble target (weighted combination)
        dataframe["&-ensemble_target"] = (
            0.5 * dataframe["&-price_change_long"] +
            0.3 * dataframe["&-price_change_short"] +
            0.1 * dataframe["&-volatility"] +
            0.1 * dataframe["&-trend_strength"]
        )
        
        return dataframe

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Populate indicators and start FreqAI.
        """
        
        # Start FreqAI
        dataframe = self.freqai.start(dataframe, metadata, self)
        
        # Additional indicators for entry/exit logic
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=self.ema_fast.value)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=self.ema_slow.value)
        
        # Bollinger Bands for additional confirmation
        bb = qtpylib.bollinger_bands(dataframe['close'], window=self.bb_period.value, stds=2)
        dataframe['bb_lower'] = bb['lower']
        dataframe['bb_middle'] = bb['mid']
        dataframe['bb_upper'] = bb['upper']
        
        # Volume indicators
        dataframe['volume_sma'] = dataframe['volume'].rolling(20).mean()
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']
        
        # ATR for volatility
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['atr_ratio'] = dataframe['atr'] / dataframe['close']
        
        return dataframe

    def populate_entry_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        Advanced entry logic combining ML predictions with technical confirmation.
        """

        # ML prediction conditions
        ml_long_condition = df["&-ensemble_target"] > self.buy_threshold.value
        ml_short_condition = df["&-ensemble_target"] < self.sell_threshold.value

        # Signal strength filter
        signal_strength = abs(df["&-ensemble_target"])
        strong_signal = signal_strength > self.min_signal_strength.value

        # Technical confirmation conditions
        trend_up = df['ema_fast'] > df['ema_slow']
        trend_down = df['ema_fast'] < df['ema_slow']

        rsi_oversold = df['rsi'] < 30
        rsi_overbought = df['rsi'] > 70
        rsi_neutral_up = (df['rsi'] > 40) & (df['rsi'] < 70)
        rsi_neutral_down = (df['rsi'] < 60) & (df['rsi'] > 30)

        # Volume confirmation
        volume_condition = True
        if self.use_volume_filter.value:
            volume_condition = df['volume_ratio'] > 1.2

        # Volatility filter
        volatility_condition = True
        if self.use_volatility_filter.value:
            volatility_condition = df['atr_ratio'] < self.volatility_threshold.value

        # Trend filter
        trend_condition_long = True
        trend_condition_short = True
        if self.use_trend_filter.value:
            trend_condition_long = trend_up
            trend_condition_short = trend_down

        # Long entry conditions
        enter_long_conditions = [
            df["do_predict"] == 1,
            ml_long_condition,
            strong_signal,
            trend_condition_long,
            volume_condition,
            volatility_condition,
            df['volume'] > 0
        ]

        # Short entry conditions
        enter_short_conditions = [
            df["do_predict"] == 1,
            ml_short_condition,
            strong_signal,
            trend_condition_short,
            volume_condition,
            volatility_condition,
            df['volume'] > 0
        ]

        # Apply conditions
        if enter_long_conditions:
            df.loc[
                reduce(lambda x, y: x & y, enter_long_conditions),
                ["enter_long", "enter_tag"]
            ] = (1, "ml_long")

        if enter_short_conditions:
            df.loc[
                reduce(lambda x, y: x & y, enter_short_conditions),
                ["enter_short", "enter_tag"]
            ] = (1, "ml_short")

        return df

    def populate_exit_trend(self, df: DataFrame, metadata: dict) -> DataFrame:
        """
        Advanced exit logic with multiple exit strategies.
        """

        # ML-based exit signals
        ml_exit_long = df["&-ensemble_target"] < -self.min_signal_strength.value
        ml_exit_short = df["&-ensemble_target"] > self.min_signal_strength.value

        # Technical exit conditions
        rsi_extreme_high = df['rsi'] > 80
        rsi_extreme_low = df['rsi'] < 20

        # Trend reversal signals
        trend_reversal_down = (df['ema_fast'] < df['ema_slow']) & (df['ema_fast'].shift(1) >= df['ema_slow'].shift(1))
        trend_reversal_up = (df['ema_fast'] > df['ema_slow']) & (df['ema_fast'].shift(1) <= df['ema_slow'].shift(1))

        # Bollinger Band exits
        bb_exit_long = df['close'] > df['bb_upper']
        bb_exit_short = df['close'] < df['bb_lower']

        # Long exit conditions
        exit_long_conditions = [
            df["do_predict"] == 1,
            (ml_exit_long | rsi_extreme_high | trend_reversal_down | bb_exit_long)
        ]

        # Short exit conditions
        exit_short_conditions = [
            df["do_predict"] == 1,
            (ml_exit_short | rsi_extreme_low | trend_reversal_up | bb_exit_short)
        ]

        # Apply exit conditions
        if exit_long_conditions:
            df.loc[
                reduce(lambda x, y: x & y, exit_long_conditions),
                ["exit_long", "exit_tag"]
            ] = (1, "ml_exit_long")

        if exit_short_conditions:
            df.loc[
                reduce(lambda x, y: x & y, exit_short_conditions),
                ["exit_short", "exit_tag"]
            ] = (1, "ml_exit_short")

        return df

    def confirm_trade_entry(
        self,
        pair: str,
        order_type: str,
        amount: float,
        rate: float,
        time_in_force: str,
        current_time,
        entry_tag,
        side: str,
        **kwargs,
    ) -> bool:
        """
        Additional trade confirmation with risk management.
        """

        df, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = df.iloc[-1].squeeze()

        # Price deviation check
        max_deviation = 0.005  # 0.5%

        if side == "long":
            if rate > (last_candle["close"] * (1 + max_deviation)):
                return False
        else:
            if rate < (last_candle["close"] * (1 - max_deviation)):
                return False

        # Signal strength check
        if abs(last_candle.get("&-ensemble_target", 0)) < self.min_signal_strength.value:
            return False

        # Volume check
        if last_candle.get("volume_ratio", 0) < 0.5:
            return False

        return True

    def custom_stake_amount(
        self,
        pair: str,
        current_time,
        current_rate: float,
        proposed_stake: float,
        min_stake: float,
        max_stake: float,
        entry_tag,
        side: str,
        **kwargs,
    ) -> float:
        """
        Dynamic position sizing based on signal strength and volatility.
        """

        df, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = df.iloc[-1].squeeze()

        # Base stake
        base_stake = proposed_stake * self.max_position_size.value

        # Signal strength multiplier
        signal_strength = abs(last_candle.get("&-ensemble_target", 0))
        strength_multiplier = min(signal_strength * 2, 1.0)

        # Volatility adjustment (reduce size in high volatility)
        volatility = last_candle.get("atr_ratio", 0.02)
        volatility_multiplier = max(0.5, 1.0 - volatility)

        # Calculate final stake
        final_stake = base_stake * strength_multiplier * volatility_multiplier

        # Ensure within bounds
        final_stake = max(min_stake, min(final_stake, max_stake))

        return final_stake
