from freqtrade.strategy import IStrategy
from datetime import datetime
import pandas as pd
from freqtrade.persistence import Trade
from typing import Optional, List, Tuple

class AODualSideStrategy(IStrategy):
    INTERFACE_VERSION = 3

    timeframe = '3m'
    max_open_trades = 1
    use_entry_signal = True
    use_exit_signal = True
    process_only_new_candles = True  # Nur 1 Trade pro Kerze

    order_types = {
        'entry': 'market',
        'exit': 'market',
        'emergency_exit': 'market',
        'force_exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    stoploss_levels: List[Tuple[float, float]] = [
        (3.0, 2.0),   # Bei > 100% Profit -> SL auf 80%
        (2.0, 1.0),   # Bei > 100% Profit -> SL auf 80%
        (1.0, 0.50),   # Bei > 100% Profit -> SL auf 80%
        (0.50, 0.20),  # Bei > 50% Profit  -> SL auf 35%
        (0.25, 0.10),  # Bei > 25% Profit  -> SL auf 15%
        (0.10, 0.03),  # Bei > 10% Profit  -> SL auf 5%
        (0.03, 0.01),  # Bei > 3% Profit   -> SL auf 1%
    ]


    minimal_roi = {
        "0": 5.0  # 500% TP
    }
    stoploss = -0.8
    startup_candle_count: int = 34

    can_short = True
    position_adjustment_enable = True

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: str | None, side: str,
                 **kwargs) -> float:
        if side == 'long':
            return 5  # Leverage for long positions
        elif side == 'short':
            return 5  # Leverage for short positions
        else:
            return 1  # Default leverage

    def populate_indicators(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        median_price = (dataframe['high'] + dataframe['low']) / 2
        dataframe['ao_fast'] = median_price.rolling(window=5).mean()
        dataframe['ao_slow'] = median_price.rolling(window=34).mean()
        dataframe['ao'] = dataframe['ao_fast'] - dataframe['ao_slow']
        return dataframe

    def populate_entry_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['enter_long'] = 0
        dataframe['enter_short'] = 0
        dataframe['enter_tag'] = None

        for i in range(len(dataframe)):
            ao_value = dataframe.at[i, 'ao']

            # Wenn AO > 0 → LONG, aber nur wenn kein anderer Trade bereits gesetzt
            if ao_value > 0:
                dataframe.at[i, 'enter_long'] = 1
                dataframe.at[i, 'enter_tag'] = 'ao_above_zero'
            # Wenn AO < 0 → SHORT, aber nur wenn LONG nicht bereits gesetzt
            elif ao_value < 0 and dataframe.at[i, 'enter_long'] == 0:
                dataframe.at[i, 'enter_short'] = 1
                dataframe.at[i, 'enter_tag'] = 'ao_below_zero'

        return dataframe

    def populate_exit_trend(self, dataframe: pd.DataFrame, metadata: dict) -> pd.DataFrame:
        dataframe['exit_long'] = 0
        dataframe['exit_short'] = 0
        dataframe['exit_tag'] = None

        for i in range(len(dataframe)):
            ao_value = dataframe.at[i, 'ao']

            if ao_value < 0:
                dataframe.at[i, 'exit_long'] = 1
                dataframe.at[i, 'exit_tag'] = 'ao_below_zero_exit'
            elif ao_value > 0:
                dataframe.at[i, 'exit_short'] = 1
                dataframe.at[i, 'exit_tag'] = 'ao_above_zero_exit'

        return dataframe

    def adjust_trade_position(self, trade: Trade, current_time: datetime,
                              current_rate: float, current_profit: float,
                              min_stake: Optional[float], max_stake: float,
                              **kwargs) -> Optional[float]:
        """
        Passt den Stop-Loss dynamisch an, basierend auf der `stoploss_levels`-Struktur.
        """
        current_stop_loss = trade.stop_loss
        new_stop_profit_level = None

        # Iteriere durch die vordefinierten Stop-Loss-Stufen
        for profit_threshold, stoploss_level in self.stoploss_levels:
            if current_profit > profit_threshold:
                new_stop_profit_level = stoploss_level
                break  # Die erste passende (höchste) Stufe wird verwendet

        if new_stop_profit_level is None:
            return None # Keine Stufe erreicht, keine Anpassung

        # Berechne den neuen absoluten Stop-Loss-Preis
        if trade.is_short:
            calculated_stop_loss = trade.open_rate * (1 - new_stop_profit_level)
            # Nur nach unten anpassen (zu Gunsten des Trades)
            if calculated_stop_loss < current_stop_loss:
                return calculated_stop_loss
        else:  # Long-Trade
            calculated_stop_loss = trade.open_rate * (1 + new_stop_profit_level)
            # Nur nach oben anpassen (zu Gunsten des Trades)
            if calculated_stop_loss > current_stop_loss:
                return calculated_stop_loss

        return None