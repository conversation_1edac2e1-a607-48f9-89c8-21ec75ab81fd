# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union
from functools import reduce

from freqtrade.strategy import (DecimalParameter, IntParameter, IStrategy)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
from technical import qtpylib


class TrendLineChannelStrategy(IStrategy):
    """
    TrendLineChannelStrategy - Dynamic Channel Detection Strategy

    This strategy is based on the "TrendLineChannelDetection.ipynb" notebook.
    It dynamically detects trend line channels by optimizing lookback periods
    and uses linear regression to fit trend lines to price highs and lows.

    Core Algorithm:
    1. Dynamically optimize lookback period to find the narrowest channel
    2. Fit linear regression lines to local highs and lows
    3. Create upper and lower trend lines forming a trading channel
    4. Enter trades when price approaches channel boundaries
    5. Exit when price reaches opposite channel boundary or breaks out

    Entry Logic:
    - Long: Price near lower trend line with upward momentum
    - Short: Price near upper trend line with downward momentum

    Exit Logic:
    - Take profit at opposite channel boundary
    - Stop loss on channel breakout
    """

    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = '4h'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.20,   # 20% ROI
        "60": 0.10,  # 10% ROI after 1 hour
        "180": 0.05, # 5% ROI after 3 hours
        "360": 0.02  # 2% ROI after 6 hours
    }

    # Optimal stoploss designed for the strategy.
    stoploss = -0.05

    # Trailing stoploss
    trailing_stop = False

    # Hyperopt parameters
    backcandles = IntParameter(50, 150, default=100, space="buy")
    brange = IntParameter(20, 80, default=50, space="buy")
    wind = IntParameter(3, 10, default=5, space="buy")
    channel_threshold = DecimalParameter(0.001, 0.01, default=0.005, space="buy")
    entry_threshold = DecimalParameter(0.1, 0.5, default=0.2, space="buy")

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 200

    # Optional order type mapping.
    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }

    plot_config = {
        'main_plot': {
            'upper_trendline': {'color': 'red'},
            'lower_trendline': {'color': 'blue'},
        },
        'subplots': {
            "Channel_Width": {
                'channel_width': {'color': 'green'},
            },
            "Channel_Position": {
                'channel_position': {'color': 'orange'},
            }
        }
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def detect_trend_lines(self, dataframe: DataFrame, candleid: int) -> dict:
        """
        Detect optimal trend lines using dynamic lookback period optimization
        """
        if candleid < self.backcandles.value + self.brange.value:
            return {
                'slmin': 0, 'slmax': 0, 'adjintercmin': 0, 'adjintercmax': 0,
                'channel_width': 0, 'optbackcandles': self.backcandles.value
            }

        optbackcandles = self.backcandles.value
        sldist = 10000  # Initialize with large value

        # Optimize lookback period
        for r1 in range(self.backcandles.value - self.brange.value,
                        self.backcandles.value + self.brange.value):
            if candleid - r1 < 0:
                continue

            maxim = np.array([])
            minim = np.array([])
            xxmin = np.array([])
            xxmax = np.array([])

            # Find local extremes in windows
            for i in range(candleid - r1, candleid + 1, self.wind.value):
                if i + self.wind.value > len(dataframe):
                    break

                window_low = dataframe['low'].iloc[i:i + self.wind.value]
                window_high = dataframe['high'].iloc[i:i + self.wind.value]

                if len(window_low) > 0 and len(window_high) > 0:
                    minim = np.append(minim, window_low.min())
                    xxmin = np.append(xxmin, window_low.idxmin())
                    maxim = np.append(maxim, window_high.max())
                    xxmax = np.append(xxmax, window_high.idxmax())

            # Fit linear regression lines
            if len(xxmin) >= 2 and len(xxmax) >= 2:
                try:
                    slmin, intercmin = np.polyfit(xxmin, minim, 1)
                    slmax, intercmax = np.polyfit(xxmax, maxim, 1)

                    # Calculate channel width at current candle
                    upper_line = slmax * candleid + intercmax
                    lower_line = slmin * candleid + intercmin
                    dist = upper_line - lower_line

                    # Select the configuration with narrowest channel
                    if dist < sldist and dist > 0:
                        sldist = dist
                        optbackcandles = r1
                        slminopt = slmin
                        slmaxopt = slmax
                        intercminopt = intercmin
                        intercmaxopt = intercmax
                        xxminopt = xxmin.copy()
                        xxmaxopt = xxmax.copy()

                except (np.linalg.LinAlgError, ValueError):
                    continue

        # Calculate adjusted intercepts to ensure lines touch actual price points
        try:
            if len(xxminopt) > 0 and len(xxmaxopt) > 0:
                adjintercmax = (dataframe['high'].iloc[xxmaxopt.astype(int)] -
                                slmaxopt * xxmaxopt).max()
                adjintercmin = (dataframe['low'].iloc[xxminopt.astype(int)] -
                                slminopt * xxminopt).min()
            else:
                adjintercmax = intercmaxopt
                adjintercmin = intercminopt

            channel_width = (slmaxopt * candleid + adjintercmax) - (slminopt * candleid + adjintercmin)

            return {
                'slmin': slminopt,
                'slmax': slmaxopt,
                'adjintercmin': adjintercmin,
                'adjintercmax': adjintercmax,
                'channel_width': channel_width,
                'optbackcandles': optbackcandles
            }
        except:
            return {
                'slmin': 0, 'slmax': 0, 'adjintercmin': 0, 'adjintercmax': 0,
                'channel_width': 0, 'optbackcandles': self.backcandles.value
            }

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds trend line indicators to the given DataFrame
        """
        # Initialize arrays for trend line data
        dataframe['upper_trendline'] = 0.0
        dataframe['lower_trendline'] = 0.0
        dataframe['channel_width'] = 0.0
        dataframe['channel_position'] = 0.0  # Position within channel (0-1)

        # Calculate trend lines for each candle
        for i in range(len(dataframe)):
            if i >= self.startup_candle_count:
                trend_data = self.detect_trend_lines(dataframe, i)

                # Calculate trend line values at current candle
                upper_line = trend_data['slmax'] * i + trend_data['adjintercmax']
                lower_line = trend_data['slmin'] * i + trend_data['adjintercmin']

                dataframe.loc[dataframe.index[i], 'upper_trendline'] = upper_line
                dataframe.loc[dataframe.index[i], 'lower_trendline'] = lower_line
                dataframe.loc[dataframe.index[i], 'channel_width'] = trend_data['channel_width']

                # Calculate position within channel (0 = at lower line, 1 = at upper line)
                if trend_data['channel_width'] > 0:
                    current_price = dataframe['close'].iloc[i]
                    position = (current_price - lower_line) / trend_data['channel_width']
                    dataframe.loc[dataframe.index[i], 'channel_position'] = max(0, min(1, position))

        # Add momentum indicators
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=14)
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on trend line analysis, populates the entry signal for the given dataframe
        """

        # Long entry conditions (price near lower trend line)
        long_conditions = [
            # Price is near lower trend line
            (dataframe['channel_position'] < self.entry_threshold.value),
            # Channel is wide enough to be meaningful
            (dataframe['channel_width'] > self.channel_threshold.value),
            # RSI shows oversold conditions
            (dataframe['rsi'] < 40),
            # Price is above lower trend line (not breaking down)
            (dataframe['close'] > dataframe['lower_trendline']),
            # Volume check
            (dataframe['volume'] > 0)
        ]

        # Short entry conditions (price near upper trend line)
        short_conditions = [
            # Price is near upper trend line
            (dataframe['channel_position'] > (1 - self.entry_threshold.value)),
            # Channel is wide enough to be meaningful
            (dataframe['channel_width'] > self.channel_threshold.value),
            # RSI shows overbought conditions
            (dataframe['rsi'] > 60),
            # Price is below upper trend line (not breaking up)
            (dataframe['close'] < dataframe['upper_trendline']),
            # Volume check
            (dataframe['volume'] > 0)
        ]

        # Combine all long conditions
        dataframe.loc[
            reduce(lambda x, y: x & y, long_conditions),
            'enter_long'] = 1

        # Combine all short conditions
        dataframe.loc[
            reduce(lambda x, y: x & y, short_conditions),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on trend line analysis, populates the exit signal for the given dataframe
        """

        # Exit long when price reaches upper trend line or breaks below lower trend line
        dataframe.loc[
            (
                # Take profit near upper trend line
                    (dataframe['channel_position'] > 0.8) |
                    # Stop loss on break below lower trend line
                    (dataframe['close'] < dataframe['lower_trendline']) |
                    # RSI becomes very overbought
                    (dataframe['rsi'] > 80)
            ),
            'exit_long'] = 1

        # Exit short when price reaches lower trend line or breaks above upper trend line
        dataframe.loc[
            (
                # Take profit near lower trend line
                    (dataframe['channel_position'] < 0.2) |
                    # Stop loss on break above upper trend line
                    (dataframe['close'] > dataframe['upper_trendline']) |
                    # RSI becomes very oversold
                    (dataframe['rsi'] < 20)
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss logic based on channel breakout
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        if trade.is_short:
            # For short trades, stop loss on break above upper trend line
            if current_rate > last_candle['upper_trendline']:
                return 0.01  # Force exit
        else:
            # For long trades, stop loss on break below lower trend line
            if current_rate < last_candle['lower_trendline']:
                return 0.01  # Force exit

        return self.stoploss

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        Custom exit logic based on channel position
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        if trade.is_short:
            # Take profit when reaching lower part of channel
            if last_candle['channel_position'] < 0.2:
                return 'channel_take_profit'
        else:
            # Take profit when reaching upper part of channel
            if last_candle['channel_position'] > 0.8:
                return 'channel_take_profit'

        return None
