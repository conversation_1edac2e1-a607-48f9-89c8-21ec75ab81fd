# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union
from functools import reduce

from freqtrade.strategy import (DecimalParameter, IntParameter, IStrategy)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
from technical import qtpylib


class GridTradingStrategy(IStrategy):
    """
    GridTradingStrategy - Grid-Based Trading System

    This strategy is based on the "grid_system_backtest.ipynb" notebook.
    It creates a price grid around a midprice and opens both long and short
    positions when price crosses any grid level. This is a market-neutral
    strategy that profits from price oscillations within the grid range.

    Core Algorithm:
    1. Generate a price grid with fixed intervals around a midprice
    2. Detect when price crosses any grid level (signal = 1)
    3. Open both long and short positions simultaneously
    4. Use grid-distance based stop loss and take profit
    5. Profit from mean reversion within the grid

    Entry Logic:
    - Signal when candle high/low spans any grid level
    - Open both long and short positions (hedging strategy)
    - Use fixed position sizes

    Exit Logic:
    - Stop Loss: 1.5 * grid_distance
    - Take Profit: 0.5 * stop_loss (asymmetric risk/reward)
    """

    INTERFACE_VERSION = 3

    # Optimal timeframe for the strategy.
    timeframe = '5m'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.10,   # 10% ROI
        "60": 0.05,  # 5% ROI after 1 hour
        "180": 0.02, # 2% ROI after 3 hours
        "360": 0.01  # 1% ROI after 6 hours
    }

    # Optimal stoploss designed for the strategy.
    stoploss = -0.02

    # Trailing stoploss
    trailing_stop = False

    # Hyperopt parameters
    grid_distance = DecimalParameter(0.002, 0.010, default=0.005, space="buy")
    midprice = DecimalParameter(1.000, 1.200, default=1.065, space="buy")
    grid_range = DecimalParameter(0.05, 0.20, default=0.10, space="buy")
    sl_multiplier = DecimalParameter(1.0, 3.0, default=1.5, space="sell")
    tp_sl_ratio = DecimalParameter(0.3, 1.0, default=0.5, space="sell")
    max_trades_per_signal = IntParameter(1, 5, default=2, space="buy")

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 20

    # Optional order type mapping.
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }

    plot_config = {
        'main_plot': {
            'grid_upper': {'color': 'red'},
            'grid_lower': {'color': 'blue'},
            'midprice_line': {'color': 'orange'},
        },
        'subplots': {
            "Grid_Signal": {
                'grid_signal': {'color': 'green'},
            },
            "Grid_Distance": {
                'distance_to_grid': {'color': 'purple'},
            }
        }
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def generate_grid(self, midprice: float, grid_distance: float, grid_range: float) -> np.ndarray:
        """
        Generate price grid levels around midprice
        """
        return np.arange(midprice - grid_range, midprice + grid_range + grid_distance, grid_distance)

    def detect_grid_cross(self, high: float, low: float, grid: np.ndarray) -> bool:
        """
        Detect if candle crosses any grid level
        Returns True if any grid level is between candle high and low
        """
        for grid_level in grid:
            if low <= grid_level <= high:
                return True
        return False

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds grid trading indicators to the given DataFrame
        """
        # Generate the price grid
        grid = self.generate_grid(self.midprice.value, self.grid_distance.value, self.grid_range.value)

        # Calculate grid signals
        dataframe['grid_signal'] = 0
        for i in range(len(dataframe)):
            if self.detect_grid_cross(dataframe['high'].iloc[i], dataframe['low'].iloc[i], grid):
                dataframe.loc[dataframe.index[i], 'grid_signal'] = 1

        # Add grid boundaries for visualization
        dataframe['grid_upper'] = self.midprice.value + self.grid_range.value
        dataframe['grid_lower'] = self.midprice.value - self.grid_range.value
        dataframe['midprice_line'] = self.midprice.value

        # Calculate distance to nearest grid level
        dataframe['distance_to_grid'] = 0.0
        for i in range(len(dataframe)):
            current_price = dataframe['close'].iloc[i]
            distances = np.abs(grid - current_price)
            dataframe.loc[dataframe.index[i], 'distance_to_grid'] = np.min(distances)

        # Calculate stop loss and take profit levels
        sl_distance = self.grid_distance.value * self.sl_multiplier.value
        tp_distance = sl_distance * self.tp_sl_ratio.value

        dataframe['sl_long'] = dataframe['close'] - sl_distance
        dataframe['tp_long'] = dataframe['close'] + tp_distance
        dataframe['sl_short'] = dataframe['close'] + sl_distance
        dataframe['tp_short'] = dataframe['close'] - tp_distance

        # Add ATR for additional analysis
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on grid signals, populates the entry signal for the given dataframe
        """

        # Grid trading conditions
        grid_conditions = [
            # Grid signal detected
            (dataframe['grid_signal'] == 1),
            # Price is within grid range
            (dataframe['close'] >= self.midprice.value - self.grid_range.value),
            (dataframe['close'] <= self.midprice.value + self.grid_range.value),
            # Volume check
            (dataframe['volume'] > 0)
        ]

        # For grid trading, we enter both long and short positions
        # This is a hedging strategy that profits from mean reversion

        # Long entry (same conditions as short - grid strategy)
        dataframe.loc[
            reduce(lambda x, y: x & y, grid_conditions),
            'enter_long'] = 1

        # Short entry (same conditions as long - grid strategy)
        dataframe.loc[
            reduce(lambda x, y: x & y, grid_conditions),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on grid analysis, populates the exit signal for the given dataframe
        """

        # Exit conditions based on distance from grid or extreme movements

        # Exit long when price moves significantly away from grid
        dataframe.loc[
            (
                # Price moves too far above grid
                    (dataframe['close'] > self.midprice.value + self.grid_range.value * 1.2) |
                    # Or when another grid signal appears (take profit)
                    (dataframe['grid_signal'] == 1)
            ),
            'exit_long'] = 1

        # Exit short when price moves significantly away from grid
        dataframe.loc[
            (
                # Price moves too far below grid
                    (dataframe['close'] < self.midprice.value - self.grid_range.value * 1.2) |
                    # Or when another grid signal appears (take profit)
                    (dataframe['grid_signal'] == 1)
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Custom stoploss logic based on grid distance
        """
        sl_distance = self.grid_distance.value * self.sl_multiplier.value

        if trade.is_short:
            # For short trades, stop loss is above entry
            sl_price = trade.open_rate + sl_distance
            return (sl_price - current_rate) / current_rate
        else:
            # For long trades, stop loss is below entry
            sl_price = trade.open_rate - sl_distance
            return (sl_price - current_rate) / current_rate

    def custom_exit(self, pair: str, trade: 'Trade', current_time: datetime, current_rate: float,
                    current_profit: float, **kwargs) -> Optional[Union[str, bool]]:
        """
        Custom exit logic based on grid take profit
        """
        sl_distance = self.grid_distance.value * self.sl_multiplier.value
        tp_distance = sl_distance * self.tp_sl_ratio.value

        if trade.is_short:
            # For short trades, take profit is below entry
            tp_price = trade.open_rate - tp_distance
            if current_rate <= tp_price:
                return 'grid_take_profit'
        else:
            # For long trades, take profit is above entry
            tp_price = trade.open_rate + tp_distance
            if current_rate >= tp_price:
                return 'grid_take_profit'

        return None

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                            time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                            side: str, **kwargs) -> bool:
        """
        Called right before placing a buy/sell order.
        Implements position limits for grid trading.
        """
        # Get current market data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1]

        # Ensure we have a valid grid signal
        if last_candle['grid_signal'] != 1:
            return False

        # Check if price is within grid range
        if (last_candle['close'] < self.midprice.value - self.grid_range.value or
                last_candle['close'] > self.midprice.value + self.grid_range.value):
            return False

        # Limit number of trades per signal (risk management)
        open_trades = len([trade for trade in self.dp.current_trades() if trade.pair == pair])
        if open_trades >= self.max_trades_per_signal.value * 2:  # *2 because we trade both directions
            return False

        return True

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        Customize leverage for grid trading (if using margin)
        """
        # Use conservative leverage for grid trading
        return min(proposed_leverage, 3.0)
