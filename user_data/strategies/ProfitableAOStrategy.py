# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file
# --- Do not remove these libs ---
import numpy as np
import pandas as pd
from pandas import DataFrame
from datetime import datetime
from typing import Optional, Union

from freqtrade.strategy import (BooleanParameter, CategoricalParameter, DecimalParameter,
                                IntParameter, IStrategy, merge_informative_pair)

# --------------------------------
# Add your lib to import here
import talib.abstract as ta
import freqtrade.vendor.qtpylib.indicators as qtpylib


class ProfitableAOStrategy(IStrategy):
    """
    Profitable Multi-Oscillator Strategy mit Awesome Oscillator als Hauptindikator

    Diese Strategie kombiniert mehrere bewährte Oszillatoren:
    - Awesome Oscillator (AO) - Hauptsignal
    - RSI - Überkauft/Überverkauft Filter
    - MACD - Trendbestätigung
    - Stochastic RSI - Feinabstimmung der Eingänge
    - Bollinger Bands - Volatilitätsfilter

    Entry-Logik:
    - Long: AO über Nulllinie + RSI nicht überkauft + MACD bullisch + StochRSI günstig
    - Short: AO unter Nulllinie + RSI nicht überverkauft + MACD bärisch + StochRSI günstig

    Risk Management:
    - Dynamische Stop-Loss basierend auf ATR
    - Trailing Stop für Gewinnmitnahme
    - Max. 1 Trade pro Candle
    """

    INTERFACE_VERSION = 3

    # Optimal timeframe für diese Strategie
    timeframe = '5m'

    # Can this strategy go short?
    can_short: bool = True

    # Minimal ROI designed for the strategy.
    minimal_roi = {
        "0": 0.15,   # 15% ROI nach 0 Minuten
        "30": 0.08,  # 8% ROI nach 30 Minuten
        "60": 0.05,  # 5% ROI nach 1 Stunde
        "120": 0.02, # 2% ROI nach 2 Stunden
        "240": 0.01  # 1% ROI nach 4 Stunden
    }

    # Optimal stoploss
    stoploss = -0.08  # 8% Stop Loss

    # Trailing stoploss
    trailing_stop = True
    trailing_only_offset_is_reached = True
    trailing_stop_positive = 0.02  # 2% trailing stop
    trailing_stop_positive_offset = 0.04  # Aktiviert bei 4% Gewinn

    # Hyperopt-Parameter für Optimierung

    # Awesome Oscillator Parameter
    ao_fast_period = IntParameter(5, 15, default=5, space="buy")
    ao_slow_period = IntParameter(30, 40, default=34, space="buy")

    # RSI Parameter
    rsi_period = IntParameter(10, 20, default=14, space="buy")
    rsi_overbought = IntParameter(70, 85, default=75, space="sell")
    rsi_oversold = IntParameter(15, 30, default=25, space="buy")

    # MACD Parameter
    macd_fast = IntParameter(8, 15, default=12, space="buy")
    macd_slow = IntParameter(20, 30, default=26, space="buy")
    macd_signal = IntParameter(7, 12, default=9, space="buy")

    # Stochastic RSI Parameter
    stoch_rsi_period = IntParameter(10, 20, default=14, space="buy")
    stoch_rsi_smooth_k = IntParameter(3, 7, default=3, space="buy")
    stoch_rsi_smooth_d = IntParameter(3, 7, default=3, space="buy")

    # Bollinger Bands Parameter
    bb_period = IntParameter(18, 25, default=20, space="buy")
    bb_std = DecimalParameter(1.8, 2.5, default=2.0, space="buy")

    # Entry/Exit Thresholds
    ao_threshold = DecimalParameter(0.1, 1.0, default=0.3, space="buy")
    volume_threshold = DecimalParameter(1.1, 2.0, default=1.5, space="buy")

    # Number of candles the strategy requires before producing valid signals
    startup_candle_count: int = 50

    # Optional order type mapping.
    order_types = {
        'entry': 'market',
        'exit': 'market',
        'stoploss': 'market',
        'stoploss_on_exchange': False
    }

    # Optional order time in force.
    order_time_in_force = {
        'entry': 'gtc',
        'exit': 'gtc'
    }

    plot_config = {
        'main_plot': {
            'bb_lowerband': {'color': 'blue'},
            'bb_middleband': {'color': 'orange'},
            'bb_upperband': {'color': 'blue'},
        },
        'subplots': {
            "AO": {
                'ao': {'color': 'purple'},
                'ao_zero': {'color': 'gray', 'type': 'line'}
            },
            "RSI": {
                'rsi': {'color': 'red'},
                'rsi_overbought': {'color': 'red', 'type': 'line'},
                'rsi_oversold': {'color': 'green', 'type': 'line'}
            },
            "MACD": {
                'macd': {'color': 'blue'},
                'macdsignal': {'color': 'orange'},
                'macdhist': {'color': 'gray', 'type': 'bar'}
            },
            "StochRSI": {
                'stoch_rsi_k': {'color': 'cyan'},
                'stoch_rsi_d': {'color': 'magenta'}
            }
        }
    }

    def informative_pairs(self):
        """
        Define additional, informative pair/interval combinations to be cached from the exchange.
        """
        return []

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Adds several different TA indicators to the given DataFrame
        """

        # Awesome Oscillator
        ao_fast_sma = ta.SMA(dataframe, timeperiod=self.ao_fast_period.value)
        ao_slow_sma = ta.SMA(dataframe, timeperiod=self.ao_slow_period.value)
        dataframe['ao'] = ao_fast_sma - ao_slow_sma
        dataframe['ao_zero'] = 0

        # Awesome Oscillator Signal Detection
        dataframe['ao_positive'] = dataframe['ao'] > 0
        dataframe['ao_negative'] = dataframe['ao'] < 0
        dataframe['ao_crossing_up'] = (dataframe['ao'] > 0) & (dataframe['ao'].shift(1) <= 0)
        dataframe['ao_crossing_down'] = (dataframe['ao'] < 0) & (dataframe['ao'].shift(1) >= 0)

        # Twin Peaks Pattern Detection
        dataframe['ao_peak'] = (dataframe['ao'] > dataframe['ao'].shift(1)) & (dataframe['ao'] > dataframe['ao'].shift(-1))
        dataframe['ao_trough'] = (dataframe['ao'] < dataframe['ao'].shift(1)) & (dataframe['ao'] < dataframe['ao'].shift(-1))

        # RSI
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        dataframe['rsi_overbought'] = self.rsi_overbought.value
        dataframe['rsi_oversold'] = self.rsi_oversold.value

        # MACD
        macd = ta.MACD(dataframe,
                       fastperiod=self.macd_fast.value,
                       slowperiod=self.macd_slow.value,
                       signalperiod=self.macd_signal.value)
        dataframe['macd'] = macd['macd']
        dataframe['macdsignal'] = macd['macdsignal']
        dataframe['macdhist'] = macd['macdhist']

        # MACD Crossover Signals
        dataframe['macd_bullish'] = dataframe['macd'] > dataframe['macdsignal']
        dataframe['macd_bearish'] = dataframe['macd'] < dataframe['macdsignal']
        dataframe['macd_cross_up'] = qtpylib.crossed_above(dataframe['macd'], dataframe['macdsignal'])
        dataframe['macd_cross_down'] = qtpylib.crossed_below(dataframe['macd'], dataframe['macdsignal'])

        # Stochastic RSI
        stoch_rsi = ta.STOCHRSI(dataframe,
                                timeperiod=self.stoch_rsi_period.value,
                                fastk_period=self.stoch_rsi_smooth_k.value,
                                fastd_period=self.stoch_rsi_smooth_d.value)
        dataframe['stoch_rsi_k'] = stoch_rsi['fastk']
        dataframe['stoch_rsi_d'] = stoch_rsi['fastd']

        # Stochastic RSI Signals
        dataframe['stoch_rsi_oversold'] = (dataframe['stoch_rsi_k'] < 20) & (dataframe['stoch_rsi_d'] < 20)
        dataframe['stoch_rsi_overbought'] = (dataframe['stoch_rsi_k'] > 80) & (dataframe['stoch_rsi_d'] > 80)
        dataframe['stoch_rsi_cross_up'] = qtpylib.crossed_above(dataframe['stoch_rsi_k'], dataframe['stoch_rsi_d'])
        dataframe['stoch_rsi_cross_down'] = qtpylib.crossed_below(dataframe['stoch_rsi_k'], dataframe['stoch_rsi_d'])

        # Bollinger Bands
        bollinger = qtpylib.bollinger_bands(dataframe['close'],
                                            window=self.bb_period.value,
                                            stds=self.bb_std.value)
        dataframe['bb_lowerband'] = bollinger['lower']
        dataframe['bb_middleband'] = bollinger['mid']
        dataframe['bb_upperband'] = bollinger['upper']
        dataframe['bb_percent'] = (dataframe['close'] - dataframe['bb_lowerband']) / (dataframe['bb_upperband'] - dataframe['bb_lowerband'])
        dataframe['bb_width'] = (dataframe['bb_upperband'] - dataframe['bb_lowerband']) / dataframe['bb_middleband']

        # ATR für dynamischen Stop Loss
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=14)

        # Volume Analysis
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']

        # Support und Resistance Levels
        dataframe['support'] = dataframe['low'].rolling(window=20).min()
        dataframe['resistance'] = dataframe['high'].rolling(window=20).max()

        # Price Action Patterns
        dataframe['hammer'] = (
                (dataframe['close'] > dataframe['open']) &
                ((dataframe['close'] - dataframe['open']) / (dataframe['high'] - dataframe['low']) > 0.6) &
                ((dataframe['open'] - dataframe['low']) / (dataframe['high'] - dataframe['low']) > 0.6)
        )

        dataframe['shooting_star'] = (
                (dataframe['open'] > dataframe['close']) &
                ((dataframe['open'] - dataframe['close']) / (dataframe['high'] - dataframe['low']) > 0.6) &
                ((dataframe['high'] - dataframe['open']) / (dataframe['high'] - dataframe['low']) > 0.6)
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the entry signal for the given dataframe
        """

        # Long Entry Conditions
        dataframe.loc[
            (
                # Hauptsignal: Awesome Oscillator
                    (dataframe['ao_crossing_up'] |
                     ((dataframe['ao'] > self.ao_threshold.value) & dataframe['ao_positive'])) &

                    # RSI Filter - nicht überkauft
                    (dataframe['rsi'] < self.rsi_overbought.value) &
                    (dataframe['rsi'] > 30) &  # Nicht zu überverkauft

                    # MACD Bestätigung
                    (dataframe['macd_bullish'] | dataframe['macd_cross_up']) &
                    (dataframe['macdhist'] > 0) &

                    # Stochastic RSI - günstiger Einstieg
                    ((dataframe['stoch_rsi_oversold'] & dataframe['stoch_rsi_cross_up']) |
                     (dataframe['stoch_rsi_k'] < 50)) &

                    # Bollinger Bands - nicht zu überkauft
                    (dataframe['bb_percent'] < 0.8) &
                    (dataframe['bb_width'] > 0.02) &  # Ausreichende Volatilität

                    # Volume Bestätigung
                    (dataframe['volume_ratio'] > self.volume_threshold.value) &

                    # Price Action Filter
                    (dataframe['close'] > dataframe['open']) &  # Bullish Candle
                    (dataframe['close'] > dataframe['bb_middleband']) &  # Über BB Mitte

                    # Zusätzliche Sicherheitsfilter
                    (dataframe['close'] > dataframe['support'] * 1.005) &  # Über Support
                    (dataframe['volume'] > 0)  # Gültiges Volume
            ),
            'enter_long'] = 1

        # Short Entry Conditions
        dataframe.loc[
            (
                # Hauptsignal: Awesome Oscillator
                    (dataframe['ao_crossing_down'] |
                     ((dataframe['ao'] < -self.ao_threshold.value) & dataframe['ao_negative'])) &

                    # RSI Filter - nicht überverkauft
                    (dataframe['rsi'] > self.rsi_oversold.value) &
                    (dataframe['rsi'] < 70) &  # Nicht zu überkauft

                    # MACD Bestätigung
                    (dataframe['macd_bearish'] | dataframe['macd_cross_down']) &
                    (dataframe['macdhist'] < 0) &

                    # Stochastic RSI - günstiger Einstieg
                    ((dataframe['stoch_rsi_overbought'] & dataframe['stoch_rsi_cross_down']) |
                     (dataframe['stoch_rsi_k'] > 50)) &

                    # Bollinger Bands - nicht zu überverkauft
                    (dataframe['bb_percent'] > 0.2) &
                    (dataframe['bb_width'] > 0.02) &  # Ausreichende Volatilität

                    # Volume Bestätigung
                    (dataframe['volume_ratio'] > self.volume_threshold.value) &

                    # Price Action Filter
                    (dataframe['close'] < dataframe['open']) &  # Bearish Candle
                    (dataframe['close'] < dataframe['bb_middleband']) &  # Unter BB Mitte

                    # Zusätzliche Sicherheitsfilter
                    (dataframe['close'] < dataframe['resistance'] * 0.995) &  # Unter Resistance
                    (dataframe['volume'] > 0)  # Gültiges Volume
            ),
            'enter_short'] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """
        Based on TA indicators, populates the exit signal for the given dataframe
        """

        # Long Exit Conditions
        dataframe.loc[
            (
                # Hauptausstiegssignal
                    (dataframe['ao_crossing_down'] |
                     (dataframe['ao'] < -self.ao_threshold.value)) |

                    # RSI überkauft
                    (dataframe['rsi'] > self.rsi_overbought.value) |

                    # MACD Trendwechsel
                    (dataframe['macd_cross_down'] & (dataframe['macdhist'] < 0)) |

                    # Stochastic RSI überkauft mit Trendwechsel
                    (dataframe['stoch_rsi_overbought'] & dataframe['stoch_rsi_cross_down']) |

                    # Bollinger Bands - stark überkauft
                    (dataframe['bb_percent'] > 0.95) |

                    # Price Action Warnsignale
                    (dataframe['shooting_star']) |

                    # Near Resistance
                    (dataframe['close'] >= dataframe['resistance'] * 0.998)
            ),
            'exit_long'] = 1

        # Short Exit Conditions
        dataframe.loc[
            (
                # Hauptausstiegssignal
                    (dataframe['ao_crossing_up'] |
                     (dataframe['ao'] > self.ao_threshold.value)) |

                    # RSI überverkauft
                    (dataframe['rsi'] < self.rsi_oversold.value) |

                    # MACD Trendwechsel
                    (dataframe['macd_cross_up'] & (dataframe['macdhist'] > 0)) |

                    # Stochastic RSI überverkauft mit Trendwechsel
                    (dataframe['stoch_rsi_oversold'] & dataframe['stoch_rsi_cross_up']) |

                    # Bollinger Bands - stark überverkauft
                    (dataframe['bb_percent'] < 0.05) |

                    # Price Action Warnsignale
                    (dataframe['hammer']) |

                    # Near Support
                    (dataframe['close'] <= dataframe['support'] * 1.002)
            ),
            'exit_short'] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                        current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Dynamischer Stop Loss basierend auf ATR
        """
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        last_candle = dataframe.iloc[-1].squeeze()

        # ATR-basierter Stop Loss
        atr_multiplier = 2.0
        if trade.is_short:
            atr_stop = current_rate + (last_candle['atr'] * atr_multiplier)
            stop_loss_pct = (atr_stop - current_rate) / current_rate
        else:
            atr_stop = current_rate - (last_candle['atr'] * atr_multiplier)
            stop_loss_pct = (current_rate - atr_stop) / current_rate

        # Mindest-Stop-Loss von 3%, maximal 12%
        return max(min(stop_loss_pct, 0.12), 0.03)

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float,
                            rate: float, time_in_force: str, current_time: datetime,
                            entry_tag: Optional[str], side: str, **kwargs) -> bool:
        """
        Bestätigt Trade-Eintrag - max. 1 Trade pro Candle
        """
        # Alternative Implementierung: Verwende interne Tracking-Variable
        # Da get_pair_trades nicht verfügbar ist, nutzen wir einen anderen Ansatz

        # Berechne aktuelle Candle-Zeit (runde auf Timeframe)
        if self.timeframe == '1m':
            candle_time = current_time.replace(second=0, microsecond=0)
        elif self.timeframe == '5m':
            candle_time = current_time.replace(minute=current_time.minute - (current_time.minute % 5), second=0, microsecond=0)
        elif self.timeframe == '15m':
            candle_time = current_time.replace(minute=current_time.minute - (current_time.minute % 15), second=0, microsecond=0)
        elif self.timeframe == '1h':
            candle_time = current_time.replace(minute=0, second=0, microsecond=0)
        else:
            candle_time = current_time.replace(second=0, microsecond=0)

        # Erstelle eindeutigen Schlüssel für Candle + Pair
        candle_key = f"{pair}_{candle_time.isoformat()}"

        # Initialisiere Tracking-Dictionary falls nicht vorhanden
        if not hasattr(self, '_candle_entries'):
            self._candle_entries = {}

        # Prüfe ob bereits ein Entry in dieser Candle stattgefunden hat
        if candle_key in self._candle_entries:
            return False

        # Markiere diese Candle als verwendet
        self._candle_entries[candle_key] = True

        # Bereinige alte Einträge (behalte nur die letzten 100)
        if len(self._candle_entries) > 100:
            # Entferne die ältesten Einträge
            old_keys = list(self._candle_entries.keys())[:-50]
            for old_key in old_keys:
                del self._candle_entries[old_key]

        return True

    def leverage(self, pair: str, current_time: datetime, current_rate: float,
                 proposed_leverage: float, max_leverage: float, entry_tag: Optional[str],
                 side: str, **kwargs) -> float:
        """
        Customize leverage for each new trade.
        """
        # Konservativer Leverage für Stabilität
        return min(proposed_leverage, 3.0)