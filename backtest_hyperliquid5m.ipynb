{"cells": [{"cell_type": "markdown", "metadata": {"id": "dcxb2PQtUE8V"}, "source": ["**<h1>Setup<h1>**\n", "\n", "---\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "Ni0tCgXTSQL9"}, "source": ["<h3>Package installation<h3>\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FQpoFECiJiLG", "outputId": "57558e0e-aa6c-484c-be4d-ef75f96bbdd7"}, "outputs": [], "source": ["#%pip install pandas\n", "#%pip install ta\n", "#%pip install matplotlib\n", "#%pip install numpy\n", "#%pip install ccxt"]}, {"cell_type": "markdown", "metadata": {"id": "G3kKKxoL1xNo"}, "source": ["<h3>Imports<h3>"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"id": "ix8j_V3gWOt5"}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import pandas as pd\n", "import ccxt\n", "import matplotlib.pyplot as plt\n", "import ta\n", "from datetime import datetime"]}, {"cell_type": "markdown", "metadata": {"id": "Abb1eUJ_Qmcl"}, "source": ["**<h1>Inputs<h1>**\n", "\n", "---\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "oES_jd7_Qwgi"}, "source": ["<h3>Data<h3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "YKmhAS0oRSKT"}, "outputs": [], "source": ["symbol = \"ASTER/USDC:USDC\"\n", "name_base = \"ASTER\"\n", "name_quote = \"USDC\"\n", "timeframe = \"5m\"\n", "starting_date_backtest = \"01 december 2024\"\n", "ending_date_backtest =  \"01 january 2026\"\n", "starting_date_dl = \"01 january 2024\"\n", "ending_date_dl = \"01 january 2026\""]}, {"cell_type": "markdown", "metadata": {"id": "ZEUKrxLKRkDC"}, "source": ["<h3>Portfolio<h3>"]}, {"cell_type": "code", "execution_count": 56, "metadata": {"id": "e_RajxXLRxTB"}, "outputs": [], "source": ["initial_capital = 1000   # in quote\n", "position_size_pct = 100         # position size in percent\n", "trade_fees = 0.045       # in percent\n", "leverage = 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>Indicators Parameters<h3>"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["params = {\n", "    \"rsi_length\": 14,\n", "    \"rsi_overbought\": 70,\n", "    # \"tp_pct\": 20,\n", "    # \"sl_pct\": 10,\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "4w2eRfduRkWh"}, "source": ["<h3>Ignores<h3>"]}, {"cell_type": "code", "execution_count": 58, "metadata": {"id": "9PEt-7ltR730"}, "outputs": [], "source": ["ignore_shorts = True\n", "ignore_longs = False\n", "\n", "ignore_tp = True\n", "ignore_sl = True\n", "ignore_exit = False"]}, {"cell_type": "markdown", "metadata": {"id": "spLsnfE7VPXd"}, "source": ["**<h1>\n", "Download Data<h1>**\n", "\n", "---"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"id": "l-R5lRhhSUmY"}, "outputs": [], "source": ["def download_data(symbol: str, timeframe: str, starting_date: str, ending_date: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Download OHLCV data from Hyperliquid exchange using CCXT.\n", "    \n", "    Args:\n", "        symbol: Trading pair (e.g. \"BTC/USDC:USDC\")\n", "        timeframe: Candle interval (e.g. \"1m\", \"5m\", \"1h\", \"1d\")\n", "        starting_date: Start date in format \"DD month YYYY\" (e.g. \"01 january 2023\")\n", "        ending_date: End date in same format\n", "    \n", "    Returns:\n", "        DataFrame with OHLCV data\n", "    \"\"\"\n", "    since = int(datetime.strptime(starting_date, \"%d %B %Y\").timestamp() * 1000)\n", "    exchange = ccxt.hyperliquid({'enableRateLimit': True})\n", "    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since)\n", "    \n", "    data = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])\n", "    data['timestamp'] = pd.to_datetime(data['timestamp'], unit='ms')\n", "    \n", "    mask = (data['timestamp'] >= pd.to_datetime(starting_date)) & (data['timestamp'] <= pd.to_datetime(ending_date))\n", "    data = data[mask]\n", "    \n", "    for col in ['open', 'high', 'low', 'close', 'volume']:\n", "        data[col] = pd.to_numeric(data[col])\n", "        \n", "    return data"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 364}, "id": "YnvF90QJSbm1", "outputId": "d0094daf-9d2a-4686-80ee-c0ef3a29448f"}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "open", "rawType": "float64", "type": "float"}, {"name": "high", "rawType": "float64", "type": "float"}, {"name": "low", "rawType": "float64", "type": "float"}, {"name": "close", "rawType": "float64", "type": "float"}, {"name": "volume", "rawType": "float64", "type": "float"}], "ref": "68f7ad20-60a0-44ae-a6a0-3091b1708697", "rows": [["0", "2025-09-19 10:30:00", "0.61", "0.68997", "0.61", "0.61", "56210.0"], ["1", "2025-09-19 10:33:00", "0.61", "0.61997", "0.61", "0.61948", "322415.0"], ["2", "2025-09-19 10:36:00", "0.61101", "0.61945", "0.61", "0.6162", "163773.0"], ["3", "2025-09-19 10:39:00", "0.6162", "0.66666", "0.615", "0.61813", "349964.0"], ["4", "2025-09-19 10:42:00", "0.61813", "0.61948", "0.60064", "0.60548", "255407.0"], ["5", "2025-09-19 10:45:00", "0.60791", "0.61975", "0.60548", "0.6101", "291287.0"], ["6", "2025-09-19 10:48:00", "0.6168", "0.6174", "0.599", "0.60103", "355345.0"], ["7", "2025-09-19 10:51:00", "0.60103", "0.61022", "0.58427", "0.59048", "369435.0"], ["8", "2025-09-19 10:54:00", "0.58785", "0.60605", "0.58503", "0.60598", "293582.0"], ["9", "2025-09-19 10:57:00", "0.60597", "0.606", "0.59001", "0.59894", "377543.0"], ["10", "2025-09-19 11:00:00", "0.59894", "0.60187", "0.5815", "0.58554", "287977.0"], ["11", "2025-09-19 11:03:00", "0.58201", "0.59591", "0.57", "0.58392", "711602.0"], ["12", "2025-09-19 11:06:00", "0.58599", "0.58691", "0.56365", "0.56629", "261148.0"], ["13", "2025-09-19 11:09:00", "0.56628", "0.57888", "0.53947", "0.54336", "634481.0"], ["14", "2025-09-19 11:12:00", "0.545", "0.605", "0.54336", "0.56841", "1061033.0"], ["15", "2025-09-19 11:15:00", "0.56779", "0.589", "0.55", "0.56304", "630842.0"], ["16", "2025-09-19 11:18:00", "0.57", "0.59328", "0.56506", "0.59328", "602139.0"], ["17", "2025-09-19 11:21:00", "0.58904", "0.6203", "0.58878", "0.60218", "1488554.0"], ["18", "2025-09-19 11:24:00", "0.60199", "0.61", "0.57702", "0.58897", "1029527.0"], ["19", "2025-09-19 11:27:00", "0.58897", "0.59594", "0.58752", "0.59476", "128909.0"], ["20", "2025-09-19 11:30:00", "0.59377", "0.605", "0.59191", "0.605", "263695.0"], ["21", "2025-09-19 11:33:00", "0.60495", "0.6192", "0.60286", "0.61388", "462480.0"], ["22", "2025-09-19 11:36:00", "0.61578", "0.617", "0.59544", "0.60383", "443260.0"], ["23", "2025-09-19 11:39:00", "0.60314", "0.6168", "0.59544", "0.6156", "295940.0"], ["24", "2025-09-19 11:42:00", "0.61628", "0.61628", "0.59872", "0.59882", "222313.0"], ["25", "2025-09-19 11:45:00", "0.59882", "0.60295", "0.59872", "0.59999", "108292.0"], ["26", "2025-09-19 11:48:00", "0.59873", "0.59931", "0.59026", "0.59457", "176147.0"], ["27", "2025-09-19 11:51:00", "0.59346", "0.595", "0.58179", "0.58594", "298699.0"], ["28", "2025-09-19 11:54:00", "0.58723", "0.6", "0.58367", "0.59135", "301974.0"], ["29", "2025-09-19 11:57:00", "0.59174", "0.6", "0.58615", "0.59379", "189163.0"], ["30", "2025-09-19 12:00:00", "0.59523", "0.62446", "0.58767", "0.62443", "828671.0"], ["31", "2025-09-19 12:03:00", "0.62446", "0.6342", "0.61105", "0.62812", "1116224.0"], ["32", "2025-09-19 12:06:00", "0.62452", "0.65555", "0.62384", "0.64535", "882620.0"], ["33", "2025-09-19 12:09:00", "0.64607", "0.66", "0.64571", "0.65424", "784941.0"], ["34", "2025-09-19 12:12:00", "0.65425", "0.65988", "0.64137", "0.64221", "859565.0"], ["35", "2025-09-19 12:15:00", "0.64138", "0.64947", "0.63662", "0.64437", "501994.0"], ["36", "2025-09-19 12:18:00", "0.64449", "0.65391", "0.63966", "0.65391", "435421.0"], ["37", "2025-09-19 12:21:00", "0.65345", "0.65482", "0.64723", "0.65167", "408103.0"], ["38", "2025-09-19 12:24:00", "0.65168", "0.67503", "0.65115", "0.65662", "532823.0"], ["39", "2025-09-19 12:27:00", "0.65599", "0.65603", "0.63661", "0.63776", "431991.0"], ["40", "2025-09-19 12:30:00", "0.63776", "0.63895", "0.61965", "0.6324", "287032.0"], ["41", "2025-09-19 12:33:00", "0.63125", "0.63654", "0.62118", "0.62227", "334620.0"], ["42", "2025-09-19 12:36:00", "0.62405", "0.6264", "0.6068", "0.62", "846617.0"], ["43", "2025-09-19 12:39:00", "0.62094", "0.62339", "0.60852", "0.61735", "509709.0"], ["44", "2025-09-19 12:42:00", "0.61809", "0.62352", "0.60862", "0.61108", "232597.0"], ["45", "2025-09-19 12:45:00", "0.6079", "0.62", "0.60321", "0.60343", "651776.0"], ["46", "2025-09-19 12:48:00", "0.60343", "0.61869", "0.59566", "0.61822", "723700.0"], ["47", "2025-09-19 12:51:00", "0.61788", "0.62413", "0.61032", "0.62243", "334965.0"], ["48", "2025-09-19 12:54:00", "0.62201", "0.63023", "0.622", "0.62883", "116906.0"], ["49", "2025-09-19 12:57:00", "0.62807", "0.63258", "0.62621", "0.62703", "45306.0"]], "shape": {"columns": 6, "rows": 975}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-09-19 10:30:00</td>\n", "      <td>0.61000</td>\n", "      <td>0.68997</td>\n", "      <td>0.61000</td>\n", "      <td>0.61000</td>\n", "      <td>56210.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-09-19 10:33:00</td>\n", "      <td>0.61000</td>\n", "      <td>0.61997</td>\n", "      <td>0.61000</td>\n", "      <td>0.61948</td>\n", "      <td>322415.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-09-19 10:36:00</td>\n", "      <td>0.61101</td>\n", "      <td>0.61945</td>\n", "      <td>0.61000</td>\n", "      <td>0.61620</td>\n", "      <td>163773.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-09-19 10:39:00</td>\n", "      <td>0.61620</td>\n", "      <td>0.66666</td>\n", "      <td>0.61500</td>\n", "      <td>0.61813</td>\n", "      <td>349964.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-09-19 10:42:00</td>\n", "      <td>0.61813</td>\n", "      <td>0.61948</td>\n", "      <td>0.60064</td>\n", "      <td>0.60548</td>\n", "      <td>255407.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>970</th>\n", "      <td>2025-09-21 11:00:00</td>\n", "      <td>1.66580</td>\n", "      <td>1.66900</td>\n", "      <td>1.63270</td>\n", "      <td>1.66020</td>\n", "      <td>1114718.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>971</th>\n", "      <td>2025-09-21 11:03:00</td>\n", "      <td>1.65980</td>\n", "      <td>1.70530</td>\n", "      <td>1.65980</td>\n", "      <td>1.70430</td>\n", "      <td>855786.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>972</th>\n", "      <td>2025-09-21 11:06:00</td>\n", "      <td>1.70430</td>\n", "      <td>1.70600</td>\n", "      <td>1.66290</td>\n", "      <td>1.67350</td>\n", "      <td>1241360.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>973</th>\n", "      <td>2025-09-21 11:09:00</td>\n", "      <td>1.67250</td>\n", "      <td>1.68350</td>\n", "      <td>1.66980</td>\n", "      <td>1.68350</td>\n", "      <td>239983.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>974</th>\n", "      <td>2025-09-21 11:12:00</td>\n", "      <td>1.68400</td>\n", "      <td>1.69900</td>\n", "      <td>1.68400</td>\n", "      <td>1.69590</td>\n", "      <td>678415.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>975 rows × 6 columns</p>\n", "</div>"], "text/plain": ["              timestamp     open     high      low    close     volume\n", "0   2025-09-19 10:30:00  0.61000  0.68997  0.61000  0.61000    56210.0\n", "1   2025-09-19 10:33:00  0.61000  0.61997  0.61000  0.61948   322415.0\n", "2   2025-09-19 10:36:00  0.61101  0.61945  0.61000  0.61620   163773.0\n", "3   2025-09-19 10:39:00  0.61620  0.66666  0.61500  0.61813   349964.0\n", "4   2025-09-19 10:42:00  0.61813  0.61948  0.60064  0.60548   255407.0\n", "..                  ...      ...      ...      ...      ...        ...\n", "970 2025-09-21 11:00:00  1.66580  1.66900  1.63270  1.66020  1114718.0\n", "971 2025-09-21 11:03:00  1.65980  1.70530  1.65980  1.70430   855786.0\n", "972 2025-09-21 11:06:00  1.70430  1.70600  1.66290  1.67350  1241360.0\n", "973 2025-09-21 11:09:00  1.67250  1.68350  1.66980  1.68350   239983.0\n", "974 2025-09-21 11:12:00  1.68400  1.69900  1.68400  1.69590   678415.0\n", "\n", "[975 rows x 6 columns]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["ohlcv_data = download_data(symbol, timeframe, starting_date_dl, ending_date_dl)\n", "ohlcv_data"]}, {"cell_type": "markdown", "metadata": {"id": "F7wlCHRvS01O"}, "source": ["**<h1>Strategy<h1>**\n", "\n", "---"]}, {"cell_type": "code", "execution_count": 61, "metadata": {"id": "w4l36OMnS6tu"}, "outputs": [], "source": ["def compute_indicators(data): # check https://technical-analysis-library-in-python.readthedocs.io/en/latest/ta.html\n", "    data['RSI'] = ta.momentum.rsi(data['close'], window=params[\"rsi_length\"])\n", "\n", "    # data['ATR'] = ta.volatility.average_true_range(data['high'], data['low'], data['close'], window=params[\"...\"])\n", "\n", "    # data['EMAf'] = ta.trend.ema_indicator(data['close'], params[\"...\"])\n", "    # data['EMAs'] = ta.trend.ema_indicator(data['close'], params[\"...\"])\n", "\n", "    # MACD = ta.trend.MACD(data['close'], window_slow=params[\"...\"], window_fast=params[\"...\"], window_sign=params[\"...\"])\n", "    # data['MACD'] = MACD.macd()\n", "    # data['MACD_histo'] = MACD.macd_diff()\n", "    # data['MACD_signal'] = MACD.macd_signal()\n", "\n", "    # BB = ta.volatility.BollingerBands(close=data['close'], window=params[\"...\"], window_dev=params[\"...\"])\n", "    # data[\"BB_lower\"] = BB.bollinger_lband()\n", "    # data[\"BB_upper\"] = BB.bollinger_hband()\n", "    # data[\"BB_avg\"] = BB.bollinger_mavg()\n", "\n", "    median_price = (data['high'] + data['low']) / 2\n", "    data['ao_fast'] = median_price.rolling(window=5).mean()\n", "    data['ao_slow'] = median_price.rolling(window=34).mean()\n", "    data['ao'] = data['ao_fast'] - data['ao_slow']\n", "\n", "    return data"]}, {"cell_type": "code", "execution_count": 62, "metadata": {"id": "Ubd_BB_USl2-"}, "outputs": [], "source": ["def prepare_data(ohlcv_data, starting_date, ending_date):\n", "    data2 = ohlcv_data.copy()\n", "    data2 = compute_indicators(data2)\n", "    data2 = data2[(data2['timestamp'] > starting_date + ' 00:00:00') & (data2['timestamp'] < ending_date + ' 00:00:00')]\n", "    data2.dropna(inplace=True)\n", "    return data2"]}, {"cell_type": "code", "execution_count": 63, "metadata": {"id": "fFPjyukjTDa7"}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "open", "rawType": "float64", "type": "float"}, {"name": "high", "rawType": "float64", "type": "float"}, {"name": "low", "rawType": "float64", "type": "float"}, {"name": "close", "rawType": "float64", "type": "float"}, {"name": "volume", "rawType": "float64", "type": "float"}, {"name": "RSI", "rawType": "float64", "type": "float"}, {"name": "ao_fast", "rawType": "float64", "type": "float"}, {"name": "ao_slow", "rawType": "float64", "type": "float"}, {"name": "ao", "rawType": "float64", "type": "float"}], "ref": "52854269-79ed-46ad-b2d5-0451f42c1f91", "rows": [["33", "2025-09-19 12:09:00", "0.64607", "0.66", "0.64571", "0.65424", "784941.0", "67.9739482363905", "0.6228629999999999", "0.6024530882352942", "0.020409911764705746"], ["34", "2025-09-19 12:12:00", "0.65425", "0.65988", "0.64137", "0.64221", "859565.0", "62.23768664421406", "0.6343729999999999", "0.6024719117647058", "0.03190108823529403"], ["35", "2025-09-19 12:15:00", "0.64138", "0.64947", "0.63662", "0.64437", "501994.0", "62.8439877447197", "0.6417689999999999", "0.6032972058823529", "0.03847179411764701"], ["36", "2025-09-19 12:18:00", "0.64449", "0.65391", "0.63966", "0.65391", "435421.0", "65.48018290074631", "0.646601", "0.6042401470588236", "0.04236085294117642"], ["37", "2025-09-19 12:21:00", "0.65345", "0.65482", "0.64723", "0.65167", "408103.0", "64.32614437218578", "0.6488669999999999", "0.60454", "0.044326999999999894"], ["38", "2025-09-19 12:24:00", "0.65168", "0.67503", "0.65115", "0.65662", "532823.0", "65.76215835077865", "0.650914", "0.606099705882353", "0.044814294117647036"], ["39", "2025-09-19 12:27:00", "0.65599", "0.65603", "0.63661", "0.63776", "431991.0", "56.43999411942607", "0.650053", "0.6070910294117646", "0.042961970588235365"], ["40", "2025-09-19 12:30:00", "0.63776", "0.63895", "0.61965", "0.6324", "287032.0", "54.09311631410663", "0.6473039999999999", "0.6077116176470587", "0.03959238235294116"], ["41", "2025-09-19 12:33:00", "0.63125", "0.63654", "0.62118", "0.62227", "334620.0", "49.87233761654378", "0.6437189999999999", "0.6086414705882353", "0.035077529411764674"], ["42", "2025-09-19 12:36:00", "0.62405", "0.6264", "0.6068", "0.62", "846617.0", "48.950595035735226", "0.636834", "0.6092608823529412", "0.02757311764705883"], ["43", "2025-09-19 12:39:00", "0.62094", "0.62339", "0.60852", "0.61735", "509709.0", "47.83902471372869", "0.627407", "0.6097888235294118", "0.017618176470588276"], ["44", "2025-09-19 12:42:00", "0.61809", "0.62352", "0.60862", "0.61108", "232597.0", "45.222414318425734", "0.6213569999999999", "0.6105060294117647", "0.010850970588235254"], ["45", "2025-09-19 12:45:00", "0.6079", "0.62", "0.60321", "0.60343", "651776.0", "42.190284594207654", "0.617818", "0.6113486764705883", "0.0064693235294116525"], ["46", "2025-09-19 12:48:00", "0.60343", "0.61869", "0.59566", "0.61822", "723700.0", "49.27192453567053", "0.6134809999999999", "0.6122867647058824", "0.0011942352941175516"], ["47", "2025-09-19 12:51:00", "0.61788", "0.62413", "0.61032", "0.62243", "334965.0", "51.10791399131762", "0.6136060000000001", "0.6139941176470589", "-0.00038811764705881657"], ["48", "2025-09-19 12:54:00", "0.62201", "0.63023", "0.622", "0.62883", "116906.0", "53.84282725309296", "0.615638", "0.6155216176470588", "0.00011638235294120225"], ["49", "2025-09-19 12:57:00", "0.62807", "0.63258", "0.62621", "0.62703", "45306.0", "52.945783585602705", "0.6183029999999999", "0.6172832352941178", "0.0010197647058821646"], ["50", "2025-09-19 13:00:00", "0.62581", "0.63679", "0.62502", "0.63606", "111511.0", "56.831348257384334", "0.622163", "0.6188048529411765", "0.003358147058823535"], ["51", "2025-09-19 13:03:00", "0.63648", "0.64016", "0.6313", "0.6313", "28833.0", "54.28655914758069", "0.6278739999999999", "0.6197222058823529", "0.008151794117646993"], ["52", "2025-09-19 13:06:00", "0.6244", "0.63", "0.6219", "0.63", "19216.0", "53.580900080680294", "0.629619", "0.6206763235294117", "0.008942676470588329"], ["53", "2025-09-19 13:09:00", "0.62999", "0.64016", "0.62972", "0.63707", "14998.0", "56.86483694826144", "0.6313840000000001", "0.6219472058823529", "0.009436794117647196"], ["54", "2025-09-19 13:12:00", "0.64107", "0.64527", "0.63891", "0.64527", "11662.0", "60.36698363783353", "0.633923", "0.623230588235294", "0.010692411764706033"], ["55", "2025-09-19 13:15:00", "0.64682", "0.67276", "0.64262", "0.66593", "626129.0", "67.5217662129056", "0.63928", "0.6246029411764705", "0.014677058823529432"], ["56", "2025-09-19 13:18:00", "0.66509", "0.68", "0.6609", "0.66675", "590109.0", "67.77045812696565", "0.646224", "0.6264920588235293", "0.019731941176470702"], ["57", "2025-09-19 13:21:00", "0.66818", "0.67332", "0.65921", "0.66331", "378442.0", "65.50441027837655", "0.6542870000000001", "0.6282610294117648", "0.026025970588235303"], ["58", "2025-09-19 13:24:00", "0.66331", "0.68397", "0.6633", "0.67558", "715562.0", "69.43072090157547", "0.662026", "0.6302061764705882", "0.03181982352941182"], ["59", "2025-09-19 13:27:00", "0.67753", "0.69038", "0.6608", "0.67476", "1077139.0", "68.86658517701285", "0.668726", "0.6324048529411764", "0.03632114705882361"], ["60", "2025-09-19 13:30:00", "0.67302", "0.674", "0.65853", "0.66425", "322904.0", "61.9219404562891", "0.6704410000000001", "0.634507205882353", "0.03593379411764708"], ["61", "2025-09-19 13:33:00", "0.666", "0.66825", "0.64981", "0.65676", "446666.0", "57.473831473184866", "0.668157", "0.6365847058823529", "0.03157229411764706"], ["62", "2025-09-19 13:36:00", "0.65679", "0.67", "0.65679", "0.66891", "429907.0", "62.215427190744535", "0.667583", "0.638689411764706", "0.028893588235294088"], ["63", "2025-09-19 13:39:00", "0.67", "0.6765", "0.66549", "0.66597", "692283.0", "60.458784576399125", "0.667055", "0.6409811764705883", "0.026073823529411677"], ["64", "2025-09-19 13:42:00", "0.66597", "0.69656", "0.66548", "0.68539", "575822.0", "67.07231180267675", "0.668141", "0.6431857352941176", "0.024955264705882385"], ["65", "2025-09-19 13:45:00", "0.68539", "0.72702", "0.68", "0.71938", "1758595.0", "74.96489503048358", "0.67559", "0.6455647058823529", "0.030025294117647094"], ["66", "2025-09-19 13:48:00", "0.71886", "0.74", "0.70616", "0.73425", "1471004.0", "77.5051931431076", "0.6884", "0.648017205882353", "0.040382794117647"], ["67", "2025-09-19 13:51:00", "0.73539", "0.74547", "0.70634", "0.71084", "1049759.0", "66.12887940112779", "0.7009019999999999", "0.6501657352941176", "0.05073626470588233"], ["68", "2025-09-19 13:54:00", "0.71035", "0.72", "0.702", "0.71821", "479283.0", "67.73455981481729", "0.7089030000000001", "0.6519414705882353", "0.0569615294117648"], ["69", "2025-09-19 13:57:00", "0.7193", "0.74001", "0.70517", "0.719", "1598068.0", "67.91016660058705", "0.717217", "0.6542810294117647", "0.0629359705882353"], ["70", "2025-09-19 14:00:00", "0.71775", "0.73196", "0.70994", "0.7296", "405884.0", "70.2498461187844", "0.720705", "0.6564623529411764", "0.06424264705882365"], ["71", "2025-09-19 14:03:00", "0.73089", "0.73089", "0.70722", "0.71231", "381606.0", "62.274118546945246", "0.7199", "0.6584632352941177", "0.06143676470588233"], ["72", "2025-09-19 14:06:00", "0.7125", "0.7125", "0.69", "0.70575", "1056712.0", "59.513332610673714", "0.714969", "0.6595855882352941", "0.05538341176470585"], ["73", "2025-09-19 14:09:00", "0.70608", "0.70608", "0.68738", "0.68857", "665777.0", "52.899127515907125", "0.712115", "0.6610682352941177", "0.05104676470588232"], ["74", "2025-09-19 14:12:00", "0.6882", "0.69454", "0.675", "0.69387", "519577.0", "54.57632070258396", "0.704551", "0.6626997058823529", "0.0418512941176471"], ["75", "2025-09-19 14:15:00", "0.693", "0.70039", "0.66459", "0.66459", "447904.0", "45.035440574093", "0.696859", "0.6642770588235295", "0.03258194117647051"], ["76", "2025-09-19 14:18:00", "0.66775", "0.67648", "0.66243", "0.67618", "384455.0", "48.847405521461845", "0.6869390000000001", "0.6658316176470588", "0.021107382352941295"], ["77", "2025-09-19 14:21:00", "0.67569", "0.71098", "0.67368", "0.70839", "1767523.0", "57.63995016123285", "0.6851550000000001", "0.6680779411764706", "0.0170770588235295"], ["78", "2025-09-19 14:24:00", "0.7084", "0.71145", "0.702", "0.7101", "544361.0", "58.05218653835241", "0.687154", "0.6707442647058823", "0.01640973529411771"], ["79", "2025-09-19 14:27:00", "0.70943", "0.71299", "0.70065", "0.707", "525790.0", "56.969794057491846", "0.691564", "0.6735447058823529", "0.018019294117647022"], ["80", "2025-09-19 14:30:00", "0.70636", "0.71362", "0.69726", "0.70848", "346302.0", "57.37837759733236", "0.6961539999999999", "0.6764348529411766", "0.019719147058823383"], ["81", "2025-09-19 14:33:00", "0.70795", "0.73", "0.70753", "0.71249", "611219.0", "58.52741943532065", "0.706016", "0.6794213235294118", "0.02659467647058822"], ["82", "2025-09-19 14:36:00", "0.71215", "0.715", "0.70451", "0.7072", "456682.0", "56.3684909519202", "0.709501", "0.6818813235294118", "0.027619676470588272"]], "shape": {"columns": 10, "rows": 942}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>RSI</th>\n", "      <th>ao_fast</th>\n", "      <th>ao_slow</th>\n", "      <th>ao</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2025-09-19 12:09:00</td>\n", "      <td>0.64607</td>\n", "      <td>0.66000</td>\n", "      <td>0.64571</td>\n", "      <td>0.65424</td>\n", "      <td>784941.0</td>\n", "      <td>67.973948</td>\n", "      <td>0.622863</td>\n", "      <td>0.602453</td>\n", "      <td>0.020410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2025-09-19 12:12:00</td>\n", "      <td>0.65425</td>\n", "      <td>0.65988</td>\n", "      <td>0.64137</td>\n", "      <td>0.64221</td>\n", "      <td>859565.0</td>\n", "      <td>62.237687</td>\n", "      <td>0.634373</td>\n", "      <td>0.602472</td>\n", "      <td>0.031901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2025-09-19 12:15:00</td>\n", "      <td>0.64138</td>\n", "      <td>0.64947</td>\n", "      <td>0.63662</td>\n", "      <td>0.64437</td>\n", "      <td>501994.0</td>\n", "      <td>62.843988</td>\n", "      <td>0.641769</td>\n", "      <td>0.603297</td>\n", "      <td>0.038472</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2025-09-19 12:18:00</td>\n", "      <td>0.64449</td>\n", "      <td>0.65391</td>\n", "      <td>0.63966</td>\n", "      <td>0.65391</td>\n", "      <td>435421.0</td>\n", "      <td>65.480183</td>\n", "      <td>0.646601</td>\n", "      <td>0.604240</td>\n", "      <td>0.042361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2025-09-19 12:21:00</td>\n", "      <td>0.65345</td>\n", "      <td>0.65482</td>\n", "      <td>0.64723</td>\n", "      <td>0.65167</td>\n", "      <td>408103.0</td>\n", "      <td>64.326144</td>\n", "      <td>0.648867</td>\n", "      <td>0.604540</td>\n", "      <td>0.044327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>970</th>\n", "      <td>2025-09-21 11:00:00</td>\n", "      <td>1.66580</td>\n", "      <td>1.66900</td>\n", "      <td>1.63270</td>\n", "      <td>1.66020</td>\n", "      <td>1114718.0</td>\n", "      <td>39.823726</td>\n", "      <td>1.680760</td>\n", "      <td>1.686850</td>\n", "      <td>-0.006090</td>\n", "    </tr>\n", "    <tr>\n", "      <th>971</th>\n", "      <td>2025-09-21 11:03:00</td>\n", "      <td>1.65980</td>\n", "      <td>1.70530</td>\n", "      <td>1.65980</td>\n", "      <td>1.70430</td>\n", "      <td>855786.0</td>\n", "      <td>50.902754</td>\n", "      <td>1.679160</td>\n", "      <td>1.686606</td>\n", "      <td>-0.007446</td>\n", "    </tr>\n", "    <tr>\n", "      <th>972</th>\n", "      <td>2025-09-21 11:06:00</td>\n", "      <td>1.70430</td>\n", "      <td>1.70600</td>\n", "      <td>1.66290</td>\n", "      <td>1.67350</td>\n", "      <td>1241360.0</td>\n", "      <td>44.711327</td>\n", "      <td>1.677900</td>\n", "      <td>1.687553</td>\n", "      <td>-0.009653</td>\n", "    </tr>\n", "    <tr>\n", "      <th>973</th>\n", "      <td>2025-09-21 11:09:00</td>\n", "      <td>1.67250</td>\n", "      <td>1.68350</td>\n", "      <td>1.66980</td>\n", "      <td>1.68350</td>\n", "      <td>239983.0</td>\n", "      <td>46.966768</td>\n", "      <td>1.674360</td>\n", "      <td>1.688543</td>\n", "      <td>-0.014183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>974</th>\n", "      <td>2025-09-21 11:12:00</td>\n", "      <td>1.68400</td>\n", "      <td>1.69900</td>\n", "      <td>1.68400</td>\n", "      <td>1.69590</td>\n", "      <td>678415.0</td>\n", "      <td>49.706533</td>\n", "      <td>1.677200</td>\n", "      <td>1.691324</td>\n", "      <td>-0.014124</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>942 rows × 10 columns</p>\n", "</div>"], "text/plain": ["              timestamp     open     high      low    close     volume  \\\n", "33  2025-09-19 12:09:00  0.64607  0.66000  0.64571  0.65424   784941.0   \n", "34  2025-09-19 12:12:00  0.65425  0.65988  0.64137  0.64221   859565.0   \n", "35  2025-09-19 12:15:00  0.64138  0.64947  0.63662  0.64437   501994.0   \n", "36  2025-09-19 12:18:00  0.64449  0.65391  0.63966  0.65391   435421.0   \n", "37  2025-09-19 12:21:00  0.65345  0.65482  0.64723  0.65167   408103.0   \n", "..                  ...      ...      ...      ...      ...        ...   \n", "970 2025-09-21 11:00:00  1.66580  1.66900  1.63270  1.66020  1114718.0   \n", "971 2025-09-21 11:03:00  1.65980  1.70530  1.65980  1.70430   855786.0   \n", "972 2025-09-21 11:06:00  1.70430  1.70600  1.66290  1.67350  1241360.0   \n", "973 2025-09-21 11:09:00  1.67250  1.68350  1.66980  1.68350   239983.0   \n", "974 2025-09-21 11:12:00  1.68400  1.69900  1.68400  1.69590   678415.0   \n", "\n", "           RSI   ao_fast   ao_slow        ao  \n", "33   67.973948  0.622863  0.602453  0.020410  \n", "34   62.237687  0.634373  0.602472  0.031901  \n", "35   62.843988  0.641769  0.603297  0.038472  \n", "36   65.480183  0.646601  0.604240  0.042361  \n", "37   64.326144  0.648867  0.604540  0.044327  \n", "..         ...       ...       ...       ...  \n", "970  39.823726  1.680760  1.686850 -0.006090  \n", "971  50.902754  1.679160  1.686606 -0.007446  \n", "972  44.711327  1.677900  1.687553 -0.009653  \n", "973  46.966768  1.674360  1.688543 -0.014183  \n", "974  49.706533  1.677200  1.691324 -0.014124  \n", "\n", "[942 rows x 10 columns]"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["data = prepare_data(ohlcv_data, starting_date_backtest, ending_date_backtest)\n", "data"]}, {"cell_type": "markdown", "metadata": {"id": "93cmN-VcSJGf"}, "source": ["<h3>Longs<h3>"]}, {"cell_type": "code", "execution_count": 64, "metadata": {"id": "y4fLfGswTQVs"}, "outputs": [], "source": ["def check_long_entry_condition(row, previous_row):\n", "    return row[\"ao\"] > 0 and previous_row[\"ao\"] < 0\n", "\n", "\n", "def check_long_exit_condition(row, previous_row):\n", "    return row[\"ao\"] < 0 and previous_row[\"ao\"] > 0\n", "\n", "\n", "def compute_long_sl_level(row, entry_price):\n", "    return entry_price * (1 + params[\"tp_pct\"] / 100)\n", "\n", "\n", "def compute_long_tp_level(row, entry_price):\n", "    return entry_price * (1 - params[\"sl_pct\"] / 100)"]}, {"cell_type": "markdown", "metadata": {"id": "lHa8jM6wSJN0"}, "source": ["<h3>Shorts<h3>"]}, {"cell_type": "code", "execution_count": 65, "metadata": {"id": "_JcIMhvOTVBQ"}, "outputs": [], "source": ["def check_short_entry_condition(row, previous_row):\n", "    return row[\"ao\"] < 0 and previous_row[\"ao\"] > 0\n", "\n", "\n", "def check_short_exit_condition(row, previous_row):\n", "    return row[\"ao\"] > 0 and previous_row[\"ao\"] < 0\n", "\n", "\n", "def compute_short_sl_level(row, entry_price):\n", "    return 1000000\n", "\n", "\n", "def compute_short_tp_level(row, entry_price):\n", "    return 0"]}, {"cell_type": "markdown", "metadata": {"id": "XmF1hYyMTcfB"}, "source": ["**<h1>Core Functions<h1>**\n", "\n", "---"]}, {"cell_type": "code", "execution_count": 66, "metadata": {"id": "G8cHv-e9Tytl"}, "outputs": [], "source": ["def calculate_position_size(balance, percentage):\n", "    return balance * percentage / 100\n", "\n", "\n", "def calculate_liquidation_price(price, leverage, order_type):\n", "        if order_type == 'long':\n", "            return price * (1 - 1 / leverage)\n", "        elif order_type == 'short':\n", "            return price * (1 + 1 / leverage)\n", "\n", "\n", "def calculate_pnl(entry_price, exit_price, quantity, order_type):\n", "    if order_type == 'long':\n", "        return (exit_price - entry_price) * quantity\n", "    elif order_type == 'short':\n", "        return (entry_price - exit_price) * quantity"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"id": "vNk2TyQqT8QO"}, "outputs": [], "source": ["def record_order(timestamp, type, price, amount, pnl, wallet, fee, orders):\n", "    order = {\n", "        'timestamp': timestamp,\n", "        'type': type,\n", "        'amount': amount,\n", "        'fee': fee,\n", "        'pnl': pnl,\n", "        'wallet': wallet,\n", "    }\n", "    orders.append(order)\n", "    print(f\"{type} at ${price} on {timestamp}, amount = {round(amount,2)}, pnl = ${round(pnl,2)}, wallet = ${round(wallet,2)}\")"]}, {"cell_type": "code", "execution_count": 68, "metadata": {"id": "HyqOw-n0UF_K"}, "outputs": [], "source": ["def run_backtest(data):\n", "\n", "    # Initialize variables\n", "    orders = []\n", "    order_in_progress = None\n", "    last_ath = 0\n", "    sl_price = 0\n", "    tp_price = 0\n", "    long_liquidation_price = 0\n", "    short_liquidation_price = 1e10\n", "    wallet = initial_capital\n", "    data['realised_pnl'] = ''\n", "    data['unrealised_pnl'] = ''\n", "    data['hodl'] = ''\n", "    data['drawdown'] = ''\n", "    previous_row = data.iloc[0]\n", "\n", "\n", "    # Go through data and make trades\n", "    for index, row in data.iterrows():\n", "        price = row['close']\n", "\n", "\n", "        # check if it is time to close a long\n", "        if order_in_progress == 'long' and not ignore_longs:\n", "            if row['low'] < long_liquidation_price:\n", "                print(f' /!\\\\ Your long was liquidated on the {row[\"timestamp\"]} (price = {long_liquidation_price} {name_quote})')\n", "                sys.exit()\n", "\n", "            elif not ignore_sl and row['low'] <= sl_price:\n", "                pnl = calculate_pnl(entry_price, sl_price, quantity, order_in_progress)\n", "                fee_exit = quantity * sl_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'long sl', sl_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_tp and row['high'] >= tp_price:\n", "                pnl = calculate_pnl(entry_price, tp_price, quantity, order_in_progress)\n", "                fee_exit = quantity * tp_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'long tp', tp_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_exit and check_long_exit_condition(row, previous_row):\n", "                pnl = calculate_pnl(entry_price, price, quantity, order_in_progress)\n", "                fee_exit = quantity * price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'long exit', price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            if wallet > last_ath:\n", "                last_ath = wallet\n", "\n", "\n", "        # check if it is time to close a short\n", "        elif order_in_progress == 'short' and not ignore_shorts:\n", "            if row['high'] > short_liquidation_price:\n", "                print(f' /!\\\\ Your short was liquidated on the {row[\"timestamp\"]} (price = {short_liquidation_price} {name_quote})')\n", "                sys.exit()\n", "\n", "            elif not ignore_sl and row['high'] >= sl_price:\n", "                pnl = calculate_pnl(entry_price, sl_price, quantity, order_in_progress)\n", "                fee_exit = quantity * sl_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'short sl', sl_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_tp and row['low'] <= tp_price:\n", "                pnl = calculate_pnl(entry_price, tp_price, quantity, order_in_progress)\n", "                fee_exit = quantity * tp_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'short tp', tp_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_exit and check_short_exit_condition(row, previous_row):\n", "                pnl = calculate_pnl(entry_price, price, quantity, order_in_progress)\n", "                fee_exit = quantity * price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'short exit', price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            if wallet > last_ath:\n", "                last_ath = wallet\n", "\n", "\n", "        # check it is time to enter a long\n", "        if not ignore_longs and order_in_progress == None:\n", "            if check_long_entry_condition(row, previous_row):\n", "                order_in_progress = 'long'\n", "                if not ignore_sl:\n", "                    sl_price = compute_long_sl_level(row, price)\n", "                if not ignore_tp:\n", "                    tp_price = compute_long_tp_level(row, price)\n", "                entry_price = price\n", "                position = calculate_position_size(wallet, position_size_pct)\n", "                amount = position * leverage\n", "                fee_entry = amount * trade_fees / 100\n", "                quantity = (amount - fee_entry) / price\n", "                long_liquidation_price = calculate_liquidation_price(price, leverage, order_in_progress)\n", "                if wallet > last_ath:\n", "                    last_ath = wallet\n", "\n", "                wallet -= position\n", "                record_order(row['timestamp'], 'long entry', price, amount-fee_entry, -fee_entry, wallet, fee_entry, orders)\n", "\n", "\n", "        # check if it is time to enter a short\n", "        if not ignore_shorts and order_in_progress == None:\n", "            if check_short_entry_condition(row, previous_row):\n", "                order_in_progress = 'short'\n", "                if not ignore_sl:\n", "                    sl_price = compute_short_sl_level(row, price)\n", "                if not ignore_tp:\n", "                    tp_price = compute_short_tp_level(row, price)\n", "                entry_price = price\n", "                position = calculate_position_size(wallet, position_size_pct)\n", "                amount = position * leverage\n", "                fee_entry = amount * trade_fees / 100\n", "                quantity = (amount - fee_entry) / price\n", "                short_liquidation_price = calculate_liquidation_price(price, leverage, order_in_progress)\n", "                wallet -= position\n", "                record_order(row['timestamp'], 'short entry', price, amount-fee_entry, -fee_entry, wallet, fee_entry, orders)\n", "\n", "\n", "        # updating wallet info\n", "        data.at[index, 'realised_pnl'] = wallet\n", "        data.at[index, 'unrealised_pnl'] = data.at[index, 'realised_pnl']\n", "        if order_in_progress != None:\n", "            data.at[index, 'unrealised_pnl'] += position + calculate_pnl(entry_price, price, quantity, order_in_progress) #- fee\n", "        data.at[index, 'hodl'] = initial_capital / data[\"close\"].iloc[0] * price\n", "        data.at[index, 'drawdown'] = (data.at[index, 'unrealised_pnl'] - last_ath) / last_ath if last_ath else 0\n", "\n", "        previous_row = row\n", "\n", "    return data, orders"]}, {"cell_type": "markdown", "metadata": {"id": "sBhyHAFlUWXo"}, "source": ["**<h1>Backtest<h1>**\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "Cqrd41wNSJRW"}, "source": ["<h3>Run<h3>"]}, {"cell_type": "code", "execution_count": 69, "metadata": {"id": "5a2mV43YUgsj"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["long entry at $0.62883 on 2025-09-19 12:54:00, amount = 999.55, pnl = $-0.45, wallet = $0.0\n", "long exit at $0.65999 on 2025-09-19 14:48:00, amount = 0, pnl = $48.61, wallet = $1048.61\n", "long entry at $0.67844 on 2025-09-19 16:09:00, amount = 1048.14, pnl = $-0.47, wallet = $0.0\n", "long exit at $0.77374 on 2025-09-19 18:48:00, amount = 0, pnl = $146.22, wallet = $1194.83\n", "long entry at $0.78389 on 2025-09-19 20:54:00, amount = 1194.29, pnl = $-0.54, wallet = $0.0\n", "long exit at $0.81679 on 2025-09-19 22:24:00, amount = 0, pnl = $49.03, wallet = $1243.86\n", "long entry at $0.82051 on 2025-09-19 22:30:00, amount = 1243.3, pnl = $-0.56, wallet = $0.0\n", "long exit at $0.88506 on 2025-09-20 01:09:00, amount = 0, pnl = $96.65, wallet = $1340.5\n", "long entry at $0.943 on 2025-09-20 01:48:00, amount = 1339.9, pnl = $-0.6, wallet = $0.0\n", "long exit at $0.9451 on 2025-09-20 03:36:00, amount = 0, pnl = $1.78, wallet = $1342.28\n", "long entry at $0.95369 on 2025-09-20 04:48:00, amount = 1341.68, pnl = $-0.6, wallet = $0.0\n", "long exit at $0.92581 on 2025-09-20 05:09:00, amount = 0, pnl = $-40.41, wallet = $1301.87\n", "long entry at $0.93283 on 2025-09-20 07:00:00, amount = 1301.28, pnl = $-0.59, wallet = $0.0\n", "long exit at $1.1489 on 2025-09-20 11:06:00, amount = 0, pnl = $300.11, wallet = $1601.97\n", "long entry at $1.2452 on 2025-09-20 11:21:00, amount = 1601.25, pnl = $-0.72, wallet = $0.0\n", "long exit at $1.1808 on 2025-09-20 11:57:00, amount = 0, pnl = $-84.22, wallet = $1517.76\n", "long entry at $1.2017 on 2025-09-20 12:27:00, amount = 1517.07, pnl = $-0.68, wallet = $0.0\n", "long exit at $1.1818 on 2025-09-20 12:48:00, amount = 0, pnl = $-26.48, wallet = $1491.28\n", "long entry at $1.2061 on 2025-09-20 13:45:00, amount = 1490.61, pnl = $-0.67, wallet = $0.0\n", "long exit at $1.1773 on 2025-09-20 14:00:00, amount = 0, pnl = $-36.92, wallet = $1454.36\n", "long entry at $1.2016 on 2025-09-20 14:21:00, amount = 1453.7, pnl = $-0.65, wallet = $0.0\n", "long exit at $1.231 on 2025-09-20 15:57:00, amount = 0, pnl = $34.24, wallet = $1488.6\n", "long entry at $1.2598 on 2025-09-20 16:39:00, amount = 1487.93, pnl = $-0.67, wallet = $-0.0\n", "long exit at $1.5523 on 2025-09-20 19:48:00, amount = 0, pnl = $343.97, wallet = $1832.58\n", "long entry at $1.57 on 2025-09-20 21:15:00, amount = 1831.75, pnl = $-0.82, wallet = $0.0\n", "long exit at $1.5474 on 2025-09-20 21:48:00, amount = 0, pnl = $-28.0, wallet = $1804.57\n", "long entry at $1.6014 on 2025-09-20 22:00:00, amount = 1803.76, pnl = $-0.81, wallet = $-0.0\n", "long exit at $1.6474 on 2025-09-20 23:15:00, amount = 0, pnl = $50.17, wallet = $1854.74\n", "long entry at $1.6649 on 2025-09-20 23:48:00, amount = 1853.9, pnl = $-0.83, wallet = $0.0\n", "long exit at $1.6039 on 2025-09-21 01:00:00, amount = 0, pnl = $-69.56, wallet = $1785.17\n", "long entry at $1.6416 on 2025-09-21 02:27:00, amount = 1784.37, pnl = $-0.8, wallet = $-0.0\n", "long exit at $1.5793 on 2025-09-21 02:45:00, amount = 0, pnl = $-69.29, wallet = $1715.88\n", "long entry at $1.6018 on 2025-09-21 03:09:00, amount = 1715.11, pnl = $-0.77, wallet = $0.0\n", "long exit at $1.778 on 2025-09-21 05:30:00, amount = 0, pnl = $187.04, wallet = $1902.91\n", "long entry at $1.8227 on 2025-09-21 05:45:00, amount = 1902.06, pnl = $-0.86, wallet = $0.0\n", "long exit at $1.8848 on 2025-09-21 07:27:00, amount = 0, pnl = $63.06, wallet = $1965.98\n", "long entry at $1.7 on 2025-09-21 10:21:00, amount = 1965.09, pnl = $-0.88, wallet = $0.0\n", "long exit at $1.6894 on 2025-09-21 10:54:00, amount = 0, pnl = $-14.02, wallet = $1951.96\n"]}], "source": ["data, backtest_orders = run_backtest(data)"]}, {"cell_type": "markdown", "metadata": {"id": "UYxUw_-NSJUq"}, "source": ["<h3>Analysis<h3>"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"id": "jFWDuCjSUrIi"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" \n", "\n", "      ** Profits ** \n", "\n", " > Period: 2025-09-19 12:09:00 -> 2025-09-21 11:12:00 \n", " > Starting balance: 1000 USDC\n", " > Final balance Bot: 1951.96 USDC\n", " > Final balance Hodl: 2592.17 USDC\n", " > Bot net profits: 95.2%\n", " > Hodl net profits: 159.22%\n", " > Net profits ratio Bot / Hodl: 0.75\n", " \n", "\n", "      ** Trades ** \n", "\n", " > Orders: 38 (19 buys, 19 sells)\n", " > Number of closed trades: 19\n", " > Winrate: 57.89%\n", " > Average trade profits: 3.89%\n", " > Number of winning trades: 11\n", " > Number of losing trades: 8\n", " > Average winning trades: 8.76%\n", " > Average losing trades: -2.8%\n", " > Best trade: 23.11% on the 2025-09-20 19:48:00\n", " > Worst trade: -5.26% on the 2025-09-20 11:57:00\n", " \n", "\n", "      ** Health ** \n", "\n", " > Maximum drawdown: -9.34%\n", " > Profit factor: 3.58\n", " > Return over maximum drawdown: 10.19\n", " \n", "\n", "      ** Fees ** \n", "\n", " > Total: 26.43 USDC\n", " > Biggest: 0.89 USDC\n", " > Average: 0.7 USDC \n", "\n"]}, {"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1000x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "## Profits\n", "show_unrealised = True\n", "show_realised = False\n", "show_hodl = True\n", "\n", "profits_bot_realised = ((data['realised_pnl'] - initial_capital)/initial_capital) * 100\n", "profits_bot_unrealised = ((data['unrealised_pnl'] - initial_capital)/initial_capital) * 100\n", "profits_hodl = ((data['hodl'] - data.iloc[0]['hodl'])/data.iloc[0]['hodl']) * 100\n", "\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10))\n", "if show_unrealised:\n", "    ax1.plot(data['timestamp'], profits_bot_unrealised, color='gold', label='Bot')\n", "if show_realised:\n", "    ax1.plot(data['timestamp'], profits_bot_realised, color='gold', label='Bot (realised)', ls= '--')\n", "if show_hodl:\n", "    ax1.plot(data['timestamp'], profits_hodl, color='purple', label='Hodl')\n", "ax1.set_title('Net Profits', fontsize=20)\n", "ax1.set_ylabel('Net Profits (%)', fontsize=18)\n", "ax1.set_xticklabels([])\n", "ax1.legend(fontsize=16)\n", "if show_unrealised:\n", "    ax2.plot(data['timestamp'], data['unrealised_pnl'], color='gold', label='Bot')\n", "if show_realised:\n", "    ax2.plot(data['timestamp'], data['realised_pnl'], color='gold', label='Bot (realised)', ls= '--')\n", "if show_hodl:\n", "    ax2.plot(data['timestamp'], data['hodl'], color='purple', label='Hodl')\n", "ax2.set_xlabel('Period', fontsize=18)\n", "ax2.set_ylabel('Net Profits (' + name_quote + ')', fontsize=18)\n", "ax2.tick_params(axis='both', which='major', labelsize=12, rotation = 45)\n", "\n", "print(f\" \\n\\n      ** Profits ** \\n\")\n", "print(f\" > Period: {data['timestamp'].iloc[0]} -> {data['timestamp'].iloc[-1]} \")\n", "print(f\" > Starting balance: {initial_capital} {name_quote}\")\n", "print(f\" > Final balance Bot: {round(data.iloc[-1]['unrealised_pnl'],2)} {name_quote}\")\n", "print(f\" > Final balance Hodl: {round(data.iloc[-1]['hodl'],2)} {name_quote}\")\n", "print(f\" > Bot net profits: {round(profits_bot_unrealised.iloc[-1],2)}%\")\n", "print(f\" > Hodl net profits: {round(profits_hodl.iloc[-1],2)}%\")\n", "print(f\" > Net profits ratio Bot / Hodl: {round(data.iloc[-1]['unrealised_pnl']/data.iloc[-1]['hodl'],2)}\")\n", "\n", "\n", "## Trades\n", "orders = pd.json_normalize(backtest_orders, sep='_')\n", "n_orders = len(orders.index)\n", "if not ignore_longs:\n", "    n_longs = orders['type'].value_counts()['long entry']\n", "else:\n", "    n_longs = 0\n", "if not ignore_shorts:\n", "    n_shorts = orders['type'].value_counts()['short entry']\n", "else:\n", "    n_shorts = 0\n", "n_entry_orders = 0\n", "if not ignore_longs:\n", "    n_entry_orders += orders['type'].value_counts()['long entry']\n", "if not ignore_shorts:\n", "    n_entry_orders += orders['type'].value_counts()['short entry']\n", "\n", "n_exit_orders = 0\n", "if 'long exit' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['long exit']\n", "if 'long tp' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['long tp']\n", "if 'long sl' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['long sl']\n", "if 'short exit' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['short exit']\n", "if 'short tp' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['short tp']\n", "if 'short sl' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['short sl']\n", "\n", "orders.loc[::2, 'pnl'] = np.nan\n", "orders['Win'] = ''\n", "orders.loc[orders['pnl']>0,'Win'] = 'Yes'\n", "orders.loc[orders['pnl']<=0,'Win'] = 'No'\n", "if 'Yes' in orders['Win'].value_counts():\n", "    n_pos_trades = orders['Win'].value_counts()['Yes']\n", "else:\n", "    n_pos_trades = 0\n", "if 'No' in orders['Win'].value_counts():\n", "    n_neg_trades = orders['Win'].value_counts()['No']\n", "else:\n", "    n_neg_trades = 0\n", "\n", "winrate = round(n_pos_trades / (n_pos_trades+n_neg_trades) * 100,2)\n", "orders['pnl%'] = orders['pnl'] / (orders['wallet'] - orders['pnl'])  * 100\n", "avg_trades = round(orders['pnl%'].mean(),2)\n", "avg_pos_trades = round(orders.loc[orders['Win'] == 'Yes']['pnl%'].mean(),2)\n", "avg_neg_trades = round(orders.loc[orders['Win'] == 'No']['pnl%'].mean(),2)\n", "best_trade = orders['pnl%'].max()\n", "when_best_trade = orders['timestamp'][orders.loc[orders['pnl%'] == best_trade].index.tolist()[0]]\n", "best_trade = round(best_trade,2)\n", "worst_trade = orders['pnl%'].min()\n", "when_worst_trade = orders['timestamp'][orders.loc[orders['pnl%'] == worst_trade].index.tolist()[0]]\n", "worst_trade = round(worst_trade,2)\n", "\n", "print(f\" \\n\\n      ** Trades ** \\n\")\n", "print(f\" > Orders: {n_orders} ({n_entry_orders} buys, {n_exit_orders} sells)\")\n", "print(f\" > Number of closed trades: {n_pos_trades+n_neg_trades}\")\n", "print(f\" > Winrate: {winrate}%\")\n", "print(f\" > Average trade profits: {avg_trades}%\")\n", "print(f\" > Number of winning trades: {n_pos_trades}\")\n", "print(f\" > Number of losing trades: {n_neg_trades}\")\n", "print(f\" > Average winning trades: {avg_pos_trades}%\")\n", "print(f\" > Average losing trades: {avg_neg_trades}%\")\n", "print(f\" > Best trade: {best_trade}% on the {when_best_trade}\")\n", "print(f\" > Worst trade: {worst_trade}% on the {when_worst_trade}\")\n", "\n", "\n", "## Health\n", "worst_drawdown = round(data['drawdown'].min()*100,2)\n", "profit_factor = round(abs(orders.loc[orders['pnl'] > 0, 'pnl'].sum() / orders.loc[orders['pnl'] < 0, 'pnl'].sum()),2)\n", "return_over_max_drawdown = round(profits_bot_unrealised.iloc[-1] / abs(worst_drawdown),2)\n", "\n", "print(f\" \\n\\n      ** Health ** \\n\")\n", "print(f\" > Maximum drawdown: {worst_drawdown}%\")\n", "print(f\" > Profit factor: {profit_factor}\")\n", "print(f\" > Return over maximum drawdown: {return_over_max_drawdown}\")\n", "\n", "\n", "## fees\n", "total_fee = round(orders['fee'].sum(),2)\n", "biggest_fee = round(orders['fee'].max(),2)\n", "avg_fee = round(orders['fee'].mean(),2)\n", "\n", "print(f\" \\n\\n      ** Fees ** \\n\")\n", "print(f\" > Total: {total_fee} {name_quote}\")\n", "print(f\" > Biggest: {biggest_fee} {name_quote}\")\n", "print(f\" > Average: {avg_fee} {name_quote} \\n\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 1}