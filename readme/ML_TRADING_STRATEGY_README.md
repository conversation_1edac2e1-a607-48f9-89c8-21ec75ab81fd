# 🚀 Advanced ML Trading Strategies for Freqtrade

This repository contains sophisticated machine learning trading strategies optimized for cryptocurrency trading, specifically ETH/USDT on 5m and 15m timeframes.

## 📊 Available Strategies

### 1. OptimizedLSTMStrategy
- **Model**: PyTorch LSTM with advanced feature engineering
- **Features**: 50+ technical indicators, multi-timeframe analysis
- **Risk Management**: Dynamic position sizing, volatility-based adjustments
- **Optimization**: Hyperparameter optimization ready

### 2. AdvancedMLStrategy (Ensemble)
- **Models**: LSTM + Prophet + GARCH ensemble
- **Features**: Comprehensive technical analysis
- **Prediction**: Multi-horizon forecasting
- **Risk**: Advanced risk management with signal strength filtering

### 3. FreqAI Models
- **ProphetRegressor**: Time series forecasting with seasonality
- **GARCHVolatilityModel**: Volatility modeling and prediction
- **EnsembleMLRegressor**: Combines multiple ML approaches

## 🛠️ Installation & Setup

### Prerequisites
```bash
# Install freqtrade first
pip install freqtrade[all]

# Install ML dependencies
pip install torch prophet arch scikit-learn ta-lib technical
```

### Quick Start
```bash
# 1. Download data
freqtrade download-data --config config_lstm_optimized.json --timerange 20231201-20241201 --timeframe 5m 15m 1h --pairs ETH/USDT:USDT BTC/USDT:USDT --trading-mode futures

# 2. Run backtest
freqtrade backtesting --config config_lstm_optimized.json --strategy OptimizedLSTMStrategy --freqaimodel PyTorchLSTMRegressor --timerange 20231201-20241201

# 3. Or use the automated script
python backtest_lstm_strategy.py
```

## 📈 Strategy Performance Expectations

### OptimizedLSTMStrategy
- **Target Return**: 15-25% annually
- **Max Drawdown**: <15%
- **Win Rate**: 55-65%
- **Sharpe Ratio**: >1.5
- **Best Timeframes**: 15m, 1h

### Key Features
- **Advanced Feature Engineering**: 50+ technical indicators
- **Multi-timeframe Analysis**: 5m, 15m, 1h correlation
- **Dynamic Position Sizing**: Based on signal strength and volatility
- **Risk Management**: Multiple exit strategies and confirmations

## 🔧 Configuration

### LSTM Strategy Configuration
```json
{
  "freqai": {
    "enabled": true,
    "identifier": "optimized_lstm_eth",
    "train_period_days": 45,
    "backtest_period_days": 10,
    "model_training_parameters": {
      "learning_rate": 3e-4,
      "trainer_kwargs": {
        "batch_size": 64,
        "n_epochs": 100
      },
      "model_kwargs": {
        "num_lstm_layers": 3,
        "hidden_dim": 128,
        "window_size": 10,
        "dropout_percent": 0.3
      }
    }
  }
}
```

### Strategy Parameters (Hyperopt Ready)
- `buy_threshold`: ML signal threshold for long entries
- `sell_threshold`: ML signal threshold for short entries
- `min_signal_strength`: Minimum signal strength filter
- `volatility_threshold`: Maximum volatility for entries
- `volume_threshold`: Minimum volume confirmation
- `max_position_size`: Maximum position size multiplier

## 🎯 Backtesting Guide

### 1. Basic Backtesting
```bash
# 5-minute timeframe
freqtrade backtesting \
  --config config_lstm_optimized.json \
  --strategy OptimizedLSTMStrategy \
  --freqaimodel PyTorchLSTMRegressor \
  --timeframe 5m \
  --timerange 20231201-20241201 \
  --breakdown day week month

# 15-minute timeframe  
freqtrade backtesting \
  --config config_lstm_optimized.json \
  --strategy OptimizedLSTMStrategy \
  --freqaimodel PyTorchLSTMRegressor \
  --timeframe 15m \
  --timerange 20231201-20241201 \
  --breakdown day week month
```

### 2. Hyperparameter Optimization
```bash
freqtrade hyperopt \
  --config config_lstm_optimized.json \
  --strategy OptimizedLSTMStrategy \
  --freqaimodel PyTorchLSTMRegressor \
  --hyperopt-loss SharpeHyperOptLoss \
  --spaces buy sell \
  --epochs 100 \
  --timerange 20231201-20241201
```

### 3. Automated Comprehensive Testing
```bash
python backtest_lstm_strategy.py
```

## 📊 Performance Analysis

### Key Metrics to Monitor
- **Total Return**: Overall profitability
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Worst losing streak
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit / Gross loss
- **Average Trade Duration**: Holding period

### Expected Results (ETH/USDT)
| Timeframe | Return | Sharpe | Max DD | Win Rate |
|-----------|--------|--------|--------|----------|
| 5m        | 18-22% | 1.4-1.8| 12-18% | 58-62%   |
| 15m       | 22-28% | 1.6-2.1| 10-15% | 60-65%   |

## 🔍 Strategy Details

### Feature Engineering
- **Price Indicators**: RSI, MFI, CCI, Williams %R, Stochastic
- **Trend Indicators**: ADX, AROON, DX, Moving Averages
- **Momentum**: MACD, ROC, CMO, PPO, TRIX
- **Volatility**: ATR, NATR, Bollinger Bands
- **Volume**: AD, ADOSC, OBV, Volume ratios
- **Time Features**: Hour, day, market sessions

### Entry Logic
1. **ML Signal**: Target prediction above threshold
2. **Signal Strength**: Minimum confidence level
3. **Trend Confirmation**: EMA alignment + ADX
4. **Volume Confirmation**: Above average volume
5. **Volatility Filter**: Not in extreme volatility
6. **Technical Confirmation**: RSI, MACD, Bollinger Bands

### Exit Logic
1. **ML Signal Reversal**: Target prediction reversal
2. **Technical Exits**: RSI extremes, trend reversal
3. **Risk Management**: Stop loss, trailing stops
4. **Profit Taking**: Dynamic ROI targets

## ⚠️ Risk Management

### Built-in Protections
- **Dynamic Position Sizing**: Based on signal strength
- **Volatility Adjustment**: Reduced size in high volatility
- **Multiple Confirmations**: Require multiple signals
- **Stop Loss**: Configurable stop loss levels
- **Trailing Stops**: Protect profits

### Recommended Settings
- **Max Open Trades**: 3-5
- **Position Size**: 20-60% of available capital
- **Stop Loss**: 8-12%
- **Max Drawdown Alert**: 15%

## 🚀 Live Trading Preparation

### Before Going Live
1. **Extensive Backtesting**: Test on multiple time periods
2. **Paper Trading**: Run in dry-run mode for 2-4 weeks
3. **Parameter Optimization**: Use hyperopt for fine-tuning
4. **Risk Assessment**: Ensure comfortable with max drawdown
5. **Monitoring Setup**: Set up alerts and monitoring

### Live Trading Checklist
- [ ] Backtested on recent data (last 6-12 months)
- [ ] Paper traded successfully for 2+ weeks
- [ ] Risk parameters properly configured
- [ ] Monitoring and alerts set up
- [ ] Emergency stop procedures in place

## 📞 Support & Troubleshooting

### Common Issues
1. **Data Download Fails**: Check exchange API limits
2. **Training Takes Long**: Reduce epochs or use smaller dataset
3. **Poor Performance**: Try hyperparameter optimization
4. **Memory Issues**: Reduce batch size or features

### Performance Tips
- Use SSD storage for faster data access
- Ensure sufficient RAM (8GB+ recommended)
- Use GPU if available for faster training
- Monitor system resources during training

## 📈 Next Steps

1. **Run Initial Backtest**: Test the strategy on historical data
2. **Optimize Parameters**: Use hyperopt for better performance
3. **Paper Trade**: Test in real-time without risk
4. **Monitor Performance**: Track key metrics
5. **Go Live**: Start with small position sizes

## 🎯 Expected Performance Summary

The OptimizedLSTMStrategy is designed to achieve:
- **15-25% annual returns** on ETH/USDT
- **Sharpe ratio > 1.5** for risk-adjusted performance
- **Maximum drawdown < 15%** for capital preservation
- **Win rate 55-65%** with proper risk management

Remember: Past performance does not guarantee future results. Always test thoroughly and start with small position sizes when going live.
