# 🔧 FreqAI Timeframe Configuration Fix

## Problem
The error `Main timeframe of 15m must be smaller or equal to FreqAI include_timeframes` occurs when the strategy's main timeframe is larger than one of the included timeframes in FreqAI configuration.

## ✅ Solution

I've created separate configurations for different timeframes:

### For 5-minute Trading:
- **Strategy**: `OptimizedLSTMStrategy.py` (timeframe = "5m")
- **Config**: `config_lstm_5m.json`
- **Include timeframes**: ["5m", "15m", "1h"]

### For 15-minute Trading:
- **Strategy**: `OptimizedLSTMStrategy15m.py` (timeframe = "15m") 
- **Config**: `config_lstm_optimized.json`
- **Include timeframes**: ["15m", "1h", "4h"]

## 🚀 Quick Start Commands

### Option 1: 5-minute Strategy
```bash
# Download data
freqtrade download-data --config config_lstm_5m.json --timerange 20241001-20241201 --timeframe 5m 15m 1h --pairs ETH/USDT:USDT BTC/USDT:USDT --trading-mode futures

# Run backtest
freqtrade backtesting --config config_lstm_5m.json --strategy OptimizedLSTMStrategy --freqaimodel PyTorchLSTMRegressor --timerange 20241001-20241201
```

### Option 2: 15-minute Strategy
```bash
# Download data
freqtrade download-data --config config_lstm_optimized.json --timerange 20241001-20241201 --timeframe 15m 1h 4h --pairs ETH/USDT:USDT BTC/USDT:USDT --trading-mode futures

# Run backtest
freqtrade backtesting --config config_lstm_optimized.json --strategy OptimizedLSTMStrategy15m --freqaimodel PyTorchLSTMRegressor --timerange 20241001-20241201
```

### Option 3: Use Automated Script
```bash
python run_lstm_backtest.py
```

## 📋 File Structure

```
├── strategies/
│   ├── OptimizedLSTMStrategy.py      # 5m timeframe
│   └── OptimizedLSTMStrategy15m.py   # 15m timeframe
├── freqaimodels/
│   └── PyTorchLSTMRegressor.py       # LSTM model
├── config_lstm_5m.json              # 5m configuration
├── config_lstm_optimized.json       # 15m configuration
└── run_lstm_backtest.py             # Automated script
```

## ⚙️ Configuration Differences

### 5-minute Config (`config_lstm_5m.json`):
- **Main timeframe**: 5m
- **Include timeframes**: ["5m", "15m", "1h"]
- **Train period**: 30 days
- **Label period**: 12 candles
- **Retrain**: Every 3 hours

### 15-minute Config (`config_lstm_optimized.json`):
- **Main timeframe**: 15m  
- **Include timeframes**: ["15m", "1h", "4h"]
- **Train period**: 45 days
- **Label period**: 20 candles
- **Retrain**: Every 4 hours

## 🎯 Recommendations

### For Beginners:
- Start with **15-minute timeframe** (less noise, more stable signals)
- Use `config_lstm_optimized.json` + `OptimizedLSTMStrategy15m.py`

### For Active Trading:
- Use **5-minute timeframe** for more frequent signals
- Use `config_lstm_5m.json` + `OptimizedLSTMStrategy.py`

## 🔍 Troubleshooting

### Error: "Main timeframe must be smaller or equal"
- **Cause**: Strategy timeframe > include_timeframes
- **Fix**: Use matching config and strategy files

### Error: "No data available"
- **Cause**: Data not downloaded for required timeframes
- **Fix**: Download data for all timeframes in config

### Error: "Strategy not found"
- **Cause**: Wrong strategy name or file missing
- **Fix**: Check strategy file exists and name matches

## 📊 Expected Performance

Both configurations are optimized for ETH/USDT trading:

| Timeframe | Signals/Day | Avg Duration | Expected Return |
|-----------|-------------|--------------|-----------------|
| 5m        | 8-15        | 2-6 hours    | 18-22% annual   |
| 15m       | 3-8         | 6-24 hours   | 22-28% annual   |

Choose based on your trading style and time availability!
