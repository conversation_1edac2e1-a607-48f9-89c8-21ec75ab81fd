#!/usr/bin/env python3
"""
Simple strategy validation script for the OptimizedMultiIndicatorStrategy
This script validates the strategy structure and parameters
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path to import strategies
sys.path.append(os.getcwd())

def validate_strategy():
    """Validate the strategy can be imported and has correct structure"""
    try:
        from strategies.ao_strategy import AggressiveProfitStrategy
        print("✓ Successfully imported AggressiveProfitStrategy")

        # Initialize strategy
        strategy = AggressiveProfitStrategy()
        print("✓ Successfully initialized strategy")

        # Check required methods
        required_methods = ['populate_indicators', 'populate_entry_trend', 'populate_exit_trend']
        for method in required_methods:
            if hasattr(strategy, method):
                print(f"✓ Method {method} exists")
            else:
                print(f"✗ Method {method} missing")
                return False

        # Check parameters
        print(f"✓ Timeframe: {strategy.timeframe}")
        print(f"✓ Can short: {strategy.can_short}")
        print(f"✓ Startup candles: {strategy.startup_candle_count}")
        print(f"✓ Stop loss: {strategy.stoploss}")
        print(f"✓ ROI: {strategy.minimal_roi}")

        # Check hyperopt parameters
        hyperopt_params = [
            'rsi_period', 'rsi_overbought', 'rsi_oversold',
            'macd_fast', 'macd_slow', 'macd_signal',
            'ema_short', 'ema_medium', 'ema_long',
            'bb_period', 'bb_std',
            'stoch_k', 'stoch_d',
            'adx_period', 'adx_threshold',
            'volume_factor', 'use_dynamic_sl', 'atr_multiplier',
            'ao_enable', 'ao_fast_period', 'ao_slow_period', 'ao_weight'
        ]

        for param in hyperopt_params:
            if hasattr(strategy, param):
                print(f"✓ Hyperopt parameter {param} exists")
            else:
                print(f"✗ Hyperopt parameter {param} missing")

        return True

    except Exception as e:
        print(f"✗ Error importing or validating strategy: {e}")
        return False

def check_data_availability():
    """Check if required data files are available"""
    data_file = "data/gateio/futures/ETH_USDT_USDT-15m-futures.feather"

    if Path(data_file).exists():
        print(f"✓ Data file found: {data_file}")
        try:
            # Try to get file size
            file_size = Path(data_file).stat().st_size
            print(f"✓ File size: {file_size / (1024*1024):.2f} MB")
            return True
        except Exception as e:
            print(f"✗ Error accessing data file: {e}")
            return False
    else:
        print(f"✗ Data file not found: {data_file}")
        return False

def main():
    """Main validation function"""
    print("=== OptimizedMultiIndicatorStrategy Validation ===\n")

    # Validate strategy
    print("1. Strategy Validation:")
    strategy_valid = validate_strategy()

    print("\n2. Data Availability:")
    data_available = check_data_availability()

    print("\n3. Summary:")
    if strategy_valid:
        print("✓ Strategy is valid and ready for backtesting")
    else:
        print("✗ Strategy has issues that need to be fixed")

    if data_available:
        print("✓ Data is available for backtesting")
    else:
        print("✗ Data is not available")

    if strategy_valid and data_available:
        print("\n✓ Ready to run backtesting with freqtrade!")
        print("\nTo run backtesting, use:")
        print("freqtrade backtesting --strategy AggressiveProfitStrategy --timeframe 15m --timerange 20231201-20241201 --config config_aggressive_profit.json")
    else:
        print("\n✗ Please fix the issues above before running backtesting")

if __name__ == "__main__":
    main()
