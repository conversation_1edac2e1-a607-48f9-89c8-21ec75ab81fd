{"cells": [{"cell_type": "markdown", "metadata": {"id": "dcxb2PQtUE8V"}, "source": ["**<h1>Setup<h1>**\n", "\n", "---\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "Ni0tCgXTSQL9"}, "source": ["<h3>Package installation<h3>\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "FQpoFECiJiLG", "outputId": "57558e0e-aa6c-484c-be4d-ef75f96bbdd7"}, "outputs": [], "source": ["#%pip install pandas\n", "#%pip install ta\n", "#%pip install matplotlib\n", "#%pip install numpy\n", "#%pip install ccxt"]}, {"cell_type": "markdown", "metadata": {"id": "G3kKKxoL1xNo"}, "source": ["<h3>Imports<h3>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "ix8j_V3gWOt5"}, "outputs": [], "source": ["import sys\n", "import numpy as np\n", "import pandas as pd\n", "import ccxt\n", "import matplotlib.pyplot as plt\n", "import ta\n", "from datetime import datetime"]}, {"cell_type": "markdown", "metadata": {"id": "Abb1eUJ_Qmcl"}, "source": ["**<h1>Inputs<h1>**\n", "\n", "---\n", "\n"]}, {"cell_type": "markdown", "metadata": {"id": "oES_jd7_Qwgi"}, "source": ["<h3>Data<h3>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "YKmhAS0oRSKT"}, "outputs": [], "source": ["symbol = \"ASTER/USDC:USDC\"\n", "name_base = \"ASTER\"\n", "name_quote = \"USDC\"\n", "timeframe = \"3m\"\n", "starting_date_backtest = \"01 december 2024\"\n", "ending_date_backtest =  \"01 january 2026\"\n", "starting_date_dl = \"01 january 2024\"\n", "ending_date_dl = \"01 january 2026\""]}, {"cell_type": "markdown", "metadata": {"id": "ZEUKrxLKRkDC"}, "source": ["<h3>Portfolio<h3>"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "e_RajxXLRxTB"}, "outputs": [], "source": ["initial_capital = 1000   # in quote\n", "position_size_pct = 100         # position size in percent\n", "trade_fees = 0.045       # in percent\n", "leverage = 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<h3>Indicators Parameters<h3>"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["params = {\n", "    \"rsi_length\": 14,\n", "    \"rsi_overbought\": 70,\n", "    # \"tp_pct\": 20,\n", "    # \"sl_pct\": 10,\n", "}"]}, {"cell_type": "markdown", "metadata": {"id": "4w2eRfduRkWh"}, "source": ["<h3>Ignores<h3>"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "9PEt-7ltR730"}, "outputs": [], "source": ["ignore_shorts = True\n", "ignore_longs = False\n", "\n", "ignore_tp = True\n", "ignore_sl = True\n", "ignore_exit = False"]}, {"cell_type": "markdown", "metadata": {"id": "spLsnfE7VPXd"}, "source": ["**<h1>\n", "Download Data<h1>**\n", "\n", "---"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "l-R5lRhhSUmY"}, "outputs": [], "source": ["def download_data(symbol: str, timeframe: str, starting_date: str, ending_date: str) -> pd.DataFrame:\n", "    \"\"\"\n", "    Download OHLCV data from Hyperliquid exchange using CCXT.\n", "    \n", "    Args:\n", "        symbol: Trading pair (e.g. \"BTC/USDC:USDC\")\n", "        timeframe: Candle interval (e.g. \"1m\", \"5m\", \"1h\", \"1d\")\n", "        starting_date: Start date in format \"DD month YYYY\" (e.g. \"01 january 2023\")\n", "        ending_date: End date in same format\n", "    \n", "    Returns:\n", "        DataFrame with OHLCV data\n", "    \"\"\"\n", "    since = int(datetime.strptime(starting_date, \"%d %B %Y\").timestamp() * 1000)\n", "    exchange = ccxt.hyperliquid({'enableRateLimit': True})\n", "    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, since=since)\n", "    \n", "    data = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])\n", "    data['timestamp'] = pd.to_datetime(data['timestamp'], unit='ms')\n", "    \n", "    mask = (data['timestamp'] >= pd.to_datetime(starting_date)) & (data['timestamp'] <= pd.to_datetime(ending_date))\n", "    data = data[mask]\n", "    \n", "    for col in ['open', 'high', 'low', 'close', 'volume']:\n", "        data[col] = pd.to_numeric(data[col])\n", "        \n", "    return data"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 364}, "id": "YnvF90QJSbm1", "outputId": "d0094daf-9d2a-4686-80ee-c0ef3a29448f"}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "open", "rawType": "float64", "type": "float"}, {"name": "high", "rawType": "float64", "type": "float"}, {"name": "low", "rawType": "float64", "type": "float"}, {"name": "close", "rawType": "float64", "type": "float"}, {"name": "volume", "rawType": "float64", "type": "float"}], "ref": "ed2b4762-4cdc-49cc-9e69-b66a3f2338f3", "rows": [["0", "2025-09-19 10:30:00", "0.61", "0.68997", "0.61", "0.61", "56210.0"], ["1", "2025-09-19 10:33:00", "0.61", "0.61997", "0.61", "0.61948", "322415.0"], ["2", "2025-09-19 10:36:00", "0.61101", "0.61945", "0.61", "0.6162", "163773.0"], ["3", "2025-09-19 10:39:00", "0.6162", "0.66666", "0.615", "0.61813", "349964.0"], ["4", "2025-09-19 10:42:00", "0.61813", "0.61948", "0.60064", "0.60548", "255407.0"], ["5", "2025-09-19 10:45:00", "0.60791", "0.61975", "0.60548", "0.6101", "291287.0"], ["6", "2025-09-19 10:48:00", "0.6168", "0.6174", "0.599", "0.60103", "355345.0"], ["7", "2025-09-19 10:51:00", "0.60103", "0.61022", "0.58427", "0.59048", "369435.0"], ["8", "2025-09-19 10:54:00", "0.58785", "0.60605", "0.58503", "0.60598", "293582.0"], ["9", "2025-09-19 10:57:00", "0.60597", "0.606", "0.59001", "0.59894", "377543.0"], ["10", "2025-09-19 11:00:00", "0.59894", "0.60187", "0.5815", "0.58554", "287977.0"], ["11", "2025-09-19 11:03:00", "0.58201", "0.59591", "0.57", "0.58392", "711602.0"], ["12", "2025-09-19 11:06:00", "0.58599", "0.58691", "0.56365", "0.56629", "261148.0"], ["13", "2025-09-19 11:09:00", "0.56628", "0.57888", "0.53947", "0.54336", "634481.0"], ["14", "2025-09-19 11:12:00", "0.545", "0.605", "0.54336", "0.56841", "1061033.0"], ["15", "2025-09-19 11:15:00", "0.56779", "0.589", "0.55", "0.56304", "630842.0"], ["16", "2025-09-19 11:18:00", "0.57", "0.59328", "0.56506", "0.59328", "602139.0"], ["17", "2025-09-19 11:21:00", "0.58904", "0.6203", "0.58878", "0.60218", "1488554.0"], ["18", "2025-09-19 11:24:00", "0.60199", "0.61", "0.57702", "0.58897", "1029527.0"], ["19", "2025-09-19 11:27:00", "0.58897", "0.59594", "0.58752", "0.59476", "128909.0"], ["20", "2025-09-19 11:30:00", "0.59377", "0.605", "0.59191", "0.605", "263695.0"], ["21", "2025-09-19 11:33:00", "0.60495", "0.6192", "0.60286", "0.61388", "462480.0"], ["22", "2025-09-19 11:36:00", "0.61578", "0.617", "0.59544", "0.60383", "443260.0"], ["23", "2025-09-19 11:39:00", "0.60314", "0.6168", "0.59544", "0.6156", "295940.0"], ["24", "2025-09-19 11:42:00", "0.61628", "0.61628", "0.59872", "0.59882", "222313.0"], ["25", "2025-09-19 11:45:00", "0.59882", "0.60295", "0.59872", "0.59999", "108292.0"], ["26", "2025-09-19 11:48:00", "0.59873", "0.59931", "0.59026", "0.59457", "176147.0"], ["27", "2025-09-19 11:51:00", "0.59346", "0.595", "0.58179", "0.58594", "298699.0"], ["28", "2025-09-19 11:54:00", "0.58723", "0.6", "0.58367", "0.59135", "301974.0"], ["29", "2025-09-19 11:57:00", "0.59174", "0.6", "0.58615", "0.59379", "189163.0"], ["30", "2025-09-19 12:00:00", "0.59523", "0.62446", "0.58767", "0.62443", "828671.0"], ["31", "2025-09-19 12:03:00", "0.62446", "0.6342", "0.61105", "0.62812", "1116224.0"], ["32", "2025-09-19 12:06:00", "0.62452", "0.65555", "0.62384", "0.64535", "882620.0"], ["33", "2025-09-19 12:09:00", "0.64607", "0.66", "0.64571", "0.65424", "784941.0"], ["34", "2025-09-19 12:12:00", "0.65425", "0.65988", "0.64137", "0.64221", "859565.0"], ["35", "2025-09-19 12:15:00", "0.64138", "0.64947", "0.63662", "0.64437", "501994.0"], ["36", "2025-09-19 12:18:00", "0.64449", "0.65391", "0.63966", "0.65391", "435421.0"], ["37", "2025-09-19 12:21:00", "0.65345", "0.65482", "0.64723", "0.65167", "408103.0"], ["38", "2025-09-19 12:24:00", "0.65168", "0.67503", "0.65115", "0.65662", "532823.0"], ["39", "2025-09-19 12:27:00", "0.65599", "0.65603", "0.63661", "0.63776", "431991.0"], ["40", "2025-09-19 12:30:00", "0.63776", "0.63895", "0.61965", "0.6324", "287032.0"], ["41", "2025-09-19 12:33:00", "0.63125", "0.63654", "0.62118", "0.62227", "334620.0"], ["42", "2025-09-19 12:36:00", "0.62405", "0.6264", "0.6068", "0.62", "846617.0"], ["43", "2025-09-19 12:39:00", "0.62094", "0.62339", "0.60852", "0.61735", "509709.0"], ["44", "2025-09-19 12:42:00", "0.61809", "0.62352", "0.60862", "0.61108", "232597.0"], ["45", "2025-09-19 12:45:00", "0.6079", "0.62", "0.60321", "0.60343", "651776.0"], ["46", "2025-09-19 12:48:00", "0.60343", "0.61869", "0.59566", "0.61822", "723700.0"], ["47", "2025-09-19 12:51:00", "0.61788", "0.62413", "0.61032", "0.62243", "334965.0"], ["48", "2025-09-19 12:54:00", "0.62201", "0.63023", "0.622", "0.62883", "116906.0"], ["49", "2025-09-19 12:57:00", "0.62807", "0.63258", "0.62621", "0.62703", "45306.0"]], "shape": {"columns": 6, "rows": 976}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-09-19 10:30:00</td>\n", "      <td>0.61000</td>\n", "      <td>0.68997</td>\n", "      <td>0.61000</td>\n", "      <td>0.61000</td>\n", "      <td>56210.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2025-09-19 10:33:00</td>\n", "      <td>0.61000</td>\n", "      <td>0.61997</td>\n", "      <td>0.61000</td>\n", "      <td>0.61948</td>\n", "      <td>322415.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2025-09-19 10:36:00</td>\n", "      <td>0.61101</td>\n", "      <td>0.61945</td>\n", "      <td>0.61000</td>\n", "      <td>0.61620</td>\n", "      <td>163773.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2025-09-19 10:39:00</td>\n", "      <td>0.61620</td>\n", "      <td>0.66666</td>\n", "      <td>0.61500</td>\n", "      <td>0.61813</td>\n", "      <td>349964.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2025-09-19 10:42:00</td>\n", "      <td>0.61813</td>\n", "      <td>0.61948</td>\n", "      <td>0.60064</td>\n", "      <td>0.60548</td>\n", "      <td>255407.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>971</th>\n", "      <td>2025-09-21 11:03:00</td>\n", "      <td>1.65980</td>\n", "      <td>1.70530</td>\n", "      <td>1.65980</td>\n", "      <td>1.70430</td>\n", "      <td>855786.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>972</th>\n", "      <td>2025-09-21 11:06:00</td>\n", "      <td>1.70430</td>\n", "      <td>1.70600</td>\n", "      <td>1.66290</td>\n", "      <td>1.67350</td>\n", "      <td>1241360.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>973</th>\n", "      <td>2025-09-21 11:09:00</td>\n", "      <td>1.67250</td>\n", "      <td>1.68350</td>\n", "      <td>1.66980</td>\n", "      <td>1.68350</td>\n", "      <td>239983.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>974</th>\n", "      <td>2025-09-21 11:12:00</td>\n", "      <td>1.68400</td>\n", "      <td>1.71500</td>\n", "      <td>1.68400</td>\n", "      <td>1.71080</td>\n", "      <td>913792.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>975</th>\n", "      <td>2025-09-21 11:15:00</td>\n", "      <td>1.70880</td>\n", "      <td>1.71490</td>\n", "      <td>1.67040</td>\n", "      <td>1.67470</td>\n", "      <td>486904.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>976 rows × 6 columns</p>\n", "</div>"], "text/plain": ["              timestamp     open     high      low    close     volume\n", "0   2025-09-19 10:30:00  0.61000  0.68997  0.61000  0.61000    56210.0\n", "1   2025-09-19 10:33:00  0.61000  0.61997  0.61000  0.61948   322415.0\n", "2   2025-09-19 10:36:00  0.61101  0.61945  0.61000  0.61620   163773.0\n", "3   2025-09-19 10:39:00  0.61620  0.66666  0.61500  0.61813   349964.0\n", "4   2025-09-19 10:42:00  0.61813  0.61948  0.60064  0.60548   255407.0\n", "..                  ...      ...      ...      ...      ...        ...\n", "971 2025-09-21 11:03:00  1.65980  1.70530  1.65980  1.70430   855786.0\n", "972 2025-09-21 11:06:00  1.70430  1.70600  1.66290  1.67350  1241360.0\n", "973 2025-09-21 11:09:00  1.67250  1.68350  1.66980  1.68350   239983.0\n", "974 2025-09-21 11:12:00  1.68400  1.71500  1.68400  1.71080   913792.0\n", "975 2025-09-21 11:15:00  1.70880  1.71490  1.67040  1.67470   486904.0\n", "\n", "[976 rows x 6 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["ohlcv_data = download_data(symbol, timeframe, starting_date_dl, ending_date_dl)\n", "ohlcv_data"]}, {"cell_type": "markdown", "metadata": {"id": "F7wlCHRvS01O"}, "source": ["**<h1>Strategy<h1>**\n", "\n", "---"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "w4l36OMnS6tu"}, "outputs": [], "source": ["def compute_indicators(data): # check https://technical-analysis-library-in-python.readthedocs.io/en/latest/ta.html\n", "    data['RSI'] = ta.momentum.rsi(data['close'], window=params[\"rsi_length\"])\n", "\n", "    # data['ATR'] = ta.volatility.average_true_range(data['high'], data['low'], data['close'], window=params[\"...\"])\n", "\n", "    # data['EMAf'] = ta.trend.ema_indicator(data['close'], params[\"...\"])\n", "    # data['EMAs'] = ta.trend.ema_indicator(data['close'], params[\"...\"])\n", "\n", "    # MACD = ta.trend.MACD(data['close'], window_slow=params[\"...\"], window_fast=params[\"...\"], window_sign=params[\"...\"])\n", "    # data['MACD'] = MACD.macd()\n", "    # data['MACD_histo'] = MACD.macd_diff()\n", "    # data['MACD_signal'] = MACD.macd_signal()\n", "\n", "    # BB = ta.volatility.BollingerBands(close=data['close'], window=params[\"...\"], window_dev=params[\"...\"])\n", "    # data[\"BB_lower\"] = BB.bollinger_lband()\n", "    # data[\"BB_upper\"] = BB.bollinger_hband()\n", "    # data[\"BB_avg\"] = BB.bollinger_mavg()\n", "\n", "    median_price = (data['high'] + data['low']) / 2\n", "    data['ao_fast'] = median_price.rolling(window=5).mean()\n", "    data['ao_slow'] = median_price.rolling(window=34).mean()\n", "    data['ao'] = data['ao_fast'] - data['ao_slow']\n", "\n", "    return data"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "Ubd_BB_USl2-"}, "outputs": [], "source": ["def prepare_data(ohlcv_data, starting_date, ending_date):\n", "    data2 = ohlcv_data.copy()\n", "    data2 = compute_indicators(data2)\n", "    data2 = data2[(data2['timestamp'] > starting_date + ' 00:00:00') & (data2['timestamp'] < ending_date + ' 00:00:00')]\n", "    data2.dropna(inplace=True)\n", "    return data2"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "fFPjyukjTDa7"}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "open", "rawType": "float64", "type": "float"}, {"name": "high", "rawType": "float64", "type": "float"}, {"name": "low", "rawType": "float64", "type": "float"}, {"name": "close", "rawType": "float64", "type": "float"}, {"name": "volume", "rawType": "float64", "type": "float"}, {"name": "RSI", "rawType": "float64", "type": "float"}, {"name": "ao_fast", "rawType": "float64", "type": "float"}, {"name": "ao_slow", "rawType": "float64", "type": "float"}, {"name": "ao", "rawType": "float64", "type": "float"}], "ref": "54d6a1cb-7f70-4b51-9c9c-b138c6ef8a43", "rows": [["33", "2025-09-19 12:09:00", "0.64607", "0.66", "0.64571", "0.65424", "784941.0", "67.9739482363905", "0.6228629999999999", "0.6024530882352942", "0.020409911764705746"], ["34", "2025-09-19 12:12:00", "0.65425", "0.65988", "0.64137", "0.64221", "859565.0", "62.23768664421406", "0.6343729999999999", "0.6024719117647058", "0.03190108823529403"], ["35", "2025-09-19 12:15:00", "0.64138", "0.64947", "0.63662", "0.64437", "501994.0", "62.8439877447197", "0.6417689999999999", "0.6032972058823529", "0.03847179411764701"], ["36", "2025-09-19 12:18:00", "0.64449", "0.65391", "0.63966", "0.65391", "435421.0", "65.48018290074631", "0.646601", "0.6042401470588236", "0.04236085294117642"], ["37", "2025-09-19 12:21:00", "0.65345", "0.65482", "0.64723", "0.65167", "408103.0", "64.32614437218578", "0.6488669999999999", "0.60454", "0.044326999999999894"], ["38", "2025-09-19 12:24:00", "0.65168", "0.67503", "0.65115", "0.65662", "532823.0", "65.76215835077865", "0.650914", "0.606099705882353", "0.044814294117647036"], ["39", "2025-09-19 12:27:00", "0.65599", "0.65603", "0.63661", "0.63776", "431991.0", "56.43999411942607", "0.650053", "0.6070910294117646", "0.042961970588235365"], ["40", "2025-09-19 12:30:00", "0.63776", "0.63895", "0.61965", "0.6324", "287032.0", "54.09311631410663", "0.6473039999999999", "0.6077116176470587", "0.03959238235294116"], ["41", "2025-09-19 12:33:00", "0.63125", "0.63654", "0.62118", "0.62227", "334620.0", "49.87233761654378", "0.6437189999999999", "0.6086414705882353", "0.035077529411764674"], ["42", "2025-09-19 12:36:00", "0.62405", "0.6264", "0.6068", "0.62", "846617.0", "48.950595035735226", "0.636834", "0.6092608823529412", "0.02757311764705883"], ["43", "2025-09-19 12:39:00", "0.62094", "0.62339", "0.60852", "0.61735", "509709.0", "47.83902471372869", "0.627407", "0.6097888235294118", "0.017618176470588276"], ["44", "2025-09-19 12:42:00", "0.61809", "0.62352", "0.60862", "0.61108", "232597.0", "45.222414318425734", "0.6213569999999999", "0.6105060294117647", "0.010850970588235254"], ["45", "2025-09-19 12:45:00", "0.6079", "0.62", "0.60321", "0.60343", "651776.0", "42.190284594207654", "0.617818", "0.6113486764705883", "0.0064693235294116525"], ["46", "2025-09-19 12:48:00", "0.60343", "0.61869", "0.59566", "0.61822", "723700.0", "49.27192453567053", "0.6134809999999999", "0.6122867647058824", "0.0011942352941175516"], ["47", "2025-09-19 12:51:00", "0.61788", "0.62413", "0.61032", "0.62243", "334965.0", "51.10791399131762", "0.6136060000000001", "0.6139941176470589", "-0.00038811764705881657"], ["48", "2025-09-19 12:54:00", "0.62201", "0.63023", "0.622", "0.62883", "116906.0", "53.84282725309296", "0.615638", "0.6155216176470588", "0.00011638235294120225"], ["49", "2025-09-19 12:57:00", "0.62807", "0.63258", "0.62621", "0.62703", "45306.0", "52.945783585602705", "0.6183029999999999", "0.6172832352941178", "0.0010197647058821646"], ["50", "2025-09-19 13:00:00", "0.62581", "0.63679", "0.62502", "0.63606", "111511.0", "56.831348257384334", "0.622163", "0.6188048529411765", "0.003358147058823535"], ["51", "2025-09-19 13:03:00", "0.63648", "0.64016", "0.6313", "0.6313", "28833.0", "54.28655914758069", "0.6278739999999999", "0.6197222058823529", "0.008151794117646993"], ["52", "2025-09-19 13:06:00", "0.6244", "0.63", "0.6219", "0.63", "19216.0", "53.580900080680294", "0.629619", "0.6206763235294117", "0.008942676470588329"], ["53", "2025-09-19 13:09:00", "0.62999", "0.64016", "0.62972", "0.63707", "14998.0", "56.86483694826144", "0.6313840000000001", "0.6219472058823529", "0.009436794117647196"], ["54", "2025-09-19 13:12:00", "0.64107", "0.64527", "0.63891", "0.64527", "11662.0", "60.36698363783353", "0.633923", "0.623230588235294", "0.010692411764706033"], ["55", "2025-09-19 13:15:00", "0.64682", "0.67276", "0.64262", "0.66593", "626129.0", "67.5217662129056", "0.63928", "0.6246029411764705", "0.014677058823529432"], ["56", "2025-09-19 13:18:00", "0.66509", "0.68", "0.6609", "0.66675", "590109.0", "67.77045812696565", "0.646224", "0.6264920588235293", "0.019731941176470702"], ["57", "2025-09-19 13:21:00", "0.66818", "0.67332", "0.65921", "0.66331", "378442.0", "65.50441027837655", "0.6542870000000001", "0.6282610294117648", "0.026025970588235303"], ["58", "2025-09-19 13:24:00", "0.66331", "0.68397", "0.6633", "0.67558", "715562.0", "69.43072090157547", "0.662026", "0.6302061764705882", "0.03181982352941182"], ["59", "2025-09-19 13:27:00", "0.67753", "0.69038", "0.6608", "0.67476", "1077139.0", "68.86658517701285", "0.668726", "0.6324048529411764", "0.03632114705882361"], ["60", "2025-09-19 13:30:00", "0.67302", "0.674", "0.65853", "0.66425", "322904.0", "61.9219404562891", "0.6704410000000001", "0.634507205882353", "0.03593379411764708"], ["61", "2025-09-19 13:33:00", "0.666", "0.66825", "0.64981", "0.65676", "446666.0", "57.473831473184866", "0.668157", "0.6365847058823529", "0.03157229411764706"], ["62", "2025-09-19 13:36:00", "0.65679", "0.67", "0.65679", "0.66891", "429907.0", "62.215427190744535", "0.667583", "0.638689411764706", "0.028893588235294088"], ["63", "2025-09-19 13:39:00", "0.67", "0.6765", "0.66549", "0.66597", "692283.0", "60.458784576399125", "0.667055", "0.6409811764705883", "0.026073823529411677"], ["64", "2025-09-19 13:42:00", "0.66597", "0.69656", "0.66548", "0.68539", "575822.0", "67.07231180267675", "0.668141", "0.6431857352941176", "0.024955264705882385"], ["65", "2025-09-19 13:45:00", "0.68539", "0.72702", "0.68", "0.71938", "1758595.0", "74.96489503048358", "0.67559", "0.6455647058823529", "0.030025294117647094"], ["66", "2025-09-19 13:48:00", "0.71886", "0.74", "0.70616", "0.73425", "1471004.0", "77.5051931431076", "0.6884", "0.648017205882353", "0.040382794117647"], ["67", "2025-09-19 13:51:00", "0.73539", "0.74547", "0.70634", "0.71084", "1049759.0", "66.12887940112779", "0.7009019999999999", "0.6501657352941176", "0.05073626470588233"], ["68", "2025-09-19 13:54:00", "0.71035", "0.72", "0.702", "0.71821", "479283.0", "67.73455981481729", "0.7089030000000001", "0.6519414705882353", "0.0569615294117648"], ["69", "2025-09-19 13:57:00", "0.7193", "0.74001", "0.70517", "0.719", "1598068.0", "67.91016660058705", "0.717217", "0.6542810294117647", "0.0629359705882353"], ["70", "2025-09-19 14:00:00", "0.71775", "0.73196", "0.70994", "0.7296", "405884.0", "70.2498461187844", "0.720705", "0.6564623529411764", "0.06424264705882365"], ["71", "2025-09-19 14:03:00", "0.73089", "0.73089", "0.70722", "0.71231", "381606.0", "62.274118546945246", "0.7199", "0.6584632352941177", "0.06143676470588233"], ["72", "2025-09-19 14:06:00", "0.7125", "0.7125", "0.69", "0.70575", "1056712.0", "59.513332610673714", "0.714969", "0.6595855882352941", "0.05538341176470585"], ["73", "2025-09-19 14:09:00", "0.70608", "0.70608", "0.68738", "0.68857", "665777.0", "52.899127515907125", "0.712115", "0.6610682352941177", "0.05104676470588232"], ["74", "2025-09-19 14:12:00", "0.6882", "0.69454", "0.675", "0.69387", "519577.0", "54.57632070258396", "0.704551", "0.6626997058823529", "0.0418512941176471"], ["75", "2025-09-19 14:15:00", "0.693", "0.70039", "0.66459", "0.66459", "447904.0", "45.035440574093", "0.696859", "0.6642770588235295", "0.03258194117647051"], ["76", "2025-09-19 14:18:00", "0.66775", "0.67648", "0.66243", "0.67618", "384455.0", "48.847405521461845", "0.6869390000000001", "0.6658316176470588", "0.021107382352941295"], ["77", "2025-09-19 14:21:00", "0.67569", "0.71098", "0.67368", "0.70839", "1767523.0", "57.63995016123285", "0.6851550000000001", "0.6680779411764706", "0.0170770588235295"], ["78", "2025-09-19 14:24:00", "0.7084", "0.71145", "0.702", "0.7101", "544361.0", "58.05218653835241", "0.687154", "0.6707442647058823", "0.01640973529411771"], ["79", "2025-09-19 14:27:00", "0.70943", "0.71299", "0.70065", "0.707", "525790.0", "56.969794057491846", "0.691564", "0.6735447058823529", "0.018019294117647022"], ["80", "2025-09-19 14:30:00", "0.70636", "0.71362", "0.69726", "0.70848", "346302.0", "57.37837759733236", "0.6961539999999999", "0.6764348529411766", "0.019719147058823383"], ["81", "2025-09-19 14:33:00", "0.70795", "0.73", "0.70753", "0.71249", "611219.0", "58.52741943532065", "0.706016", "0.6794213235294118", "0.02659467647058822"], ["82", "2025-09-19 14:36:00", "0.71215", "0.715", "0.70451", "0.7072", "456682.0", "56.3684909519202", "0.709501", "0.6818813235294118", "0.027619676470588272"]], "shape": {"columns": 10, "rows": 943}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>volume</th>\n", "      <th>RSI</th>\n", "      <th>ao_fast</th>\n", "      <th>ao_slow</th>\n", "      <th>ao</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>2025-09-19 12:09:00</td>\n", "      <td>0.64607</td>\n", "      <td>0.66000</td>\n", "      <td>0.64571</td>\n", "      <td>0.65424</td>\n", "      <td>784941.0</td>\n", "      <td>67.973948</td>\n", "      <td>0.622863</td>\n", "      <td>0.602453</td>\n", "      <td>0.020410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>2025-09-19 12:12:00</td>\n", "      <td>0.65425</td>\n", "      <td>0.65988</td>\n", "      <td>0.64137</td>\n", "      <td>0.64221</td>\n", "      <td>859565.0</td>\n", "      <td>62.237687</td>\n", "      <td>0.634373</td>\n", "      <td>0.602472</td>\n", "      <td>0.031901</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>2025-09-19 12:15:00</td>\n", "      <td>0.64138</td>\n", "      <td>0.64947</td>\n", "      <td>0.63662</td>\n", "      <td>0.64437</td>\n", "      <td>501994.0</td>\n", "      <td>62.843988</td>\n", "      <td>0.641769</td>\n", "      <td>0.603297</td>\n", "      <td>0.038472</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>2025-09-19 12:18:00</td>\n", "      <td>0.64449</td>\n", "      <td>0.65391</td>\n", "      <td>0.63966</td>\n", "      <td>0.65391</td>\n", "      <td>435421.0</td>\n", "      <td>65.480183</td>\n", "      <td>0.646601</td>\n", "      <td>0.604240</td>\n", "      <td>0.042361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37</th>\n", "      <td>2025-09-19 12:21:00</td>\n", "      <td>0.65345</td>\n", "      <td>0.65482</td>\n", "      <td>0.64723</td>\n", "      <td>0.65167</td>\n", "      <td>408103.0</td>\n", "      <td>64.326144</td>\n", "      <td>0.648867</td>\n", "      <td>0.604540</td>\n", "      <td>0.044327</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>971</th>\n", "      <td>2025-09-21 11:03:00</td>\n", "      <td>1.65980</td>\n", "      <td>1.70530</td>\n", "      <td>1.65980</td>\n", "      <td>1.70430</td>\n", "      <td>855786.0</td>\n", "      <td>50.902754</td>\n", "      <td>1.679160</td>\n", "      <td>1.686606</td>\n", "      <td>-0.007446</td>\n", "    </tr>\n", "    <tr>\n", "      <th>972</th>\n", "      <td>2025-09-21 11:06:00</td>\n", "      <td>1.70430</td>\n", "      <td>1.70600</td>\n", "      <td>1.66290</td>\n", "      <td>1.67350</td>\n", "      <td>1241360.0</td>\n", "      <td>44.711327</td>\n", "      <td>1.677900</td>\n", "      <td>1.687553</td>\n", "      <td>-0.009653</td>\n", "    </tr>\n", "    <tr>\n", "      <th>973</th>\n", "      <td>2025-09-21 11:09:00</td>\n", "      <td>1.67250</td>\n", "      <td>1.68350</td>\n", "      <td>1.66980</td>\n", "      <td>1.68350</td>\n", "      <td>239983.0</td>\n", "      <td>46.966768</td>\n", "      <td>1.674360</td>\n", "      <td>1.688543</td>\n", "      <td>-0.014183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>974</th>\n", "      <td>2025-09-21 11:12:00</td>\n", "      <td>1.68400</td>\n", "      <td>1.71500</td>\n", "      <td>1.68400</td>\n", "      <td>1.71080</td>\n", "      <td>913792.0</td>\n", "      <td>52.646114</td>\n", "      <td>1.678800</td>\n", "      <td>1.691559</td>\n", "      <td>-0.012759</td>\n", "    </tr>\n", "    <tr>\n", "      <th>975</th>\n", "      <td>2025-09-21 11:15:00</td>\n", "      <td>1.70880</td>\n", "      <td>1.71490</td>\n", "      <td>1.67040</td>\n", "      <td>1.67470</td>\n", "      <td>486904.0</td>\n", "      <td>45.679792</td>\n", "      <td>1.687160</td>\n", "      <td>1.694249</td>\n", "      <td>-0.007089</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>943 rows × 10 columns</p>\n", "</div>"], "text/plain": ["              timestamp     open     high      low    close     volume  \\\n", "33  2025-09-19 12:09:00  0.64607  0.66000  0.64571  0.65424   784941.0   \n", "34  2025-09-19 12:12:00  0.65425  0.65988  0.64137  0.64221   859565.0   \n", "35  2025-09-19 12:15:00  0.64138  0.64947  0.63662  0.64437   501994.0   \n", "36  2025-09-19 12:18:00  0.64449  0.65391  0.63966  0.65391   435421.0   \n", "37  2025-09-19 12:21:00  0.65345  0.65482  0.64723  0.65167   408103.0   \n", "..                  ...      ...      ...      ...      ...        ...   \n", "971 2025-09-21 11:03:00  1.65980  1.70530  1.65980  1.70430   855786.0   \n", "972 2025-09-21 11:06:00  1.70430  1.70600  1.66290  1.67350  1241360.0   \n", "973 2025-09-21 11:09:00  1.67250  1.68350  1.66980  1.68350   239983.0   \n", "974 2025-09-21 11:12:00  1.68400  1.71500  1.68400  1.71080   913792.0   \n", "975 2025-09-21 11:15:00  1.70880  1.71490  1.67040  1.67470   486904.0   \n", "\n", "           RSI   ao_fast   ao_slow        ao  \n", "33   67.973948  0.622863  0.602453  0.020410  \n", "34   62.237687  0.634373  0.602472  0.031901  \n", "35   62.843988  0.641769  0.603297  0.038472  \n", "36   65.480183  0.646601  0.604240  0.042361  \n", "37   64.326144  0.648867  0.604540  0.044327  \n", "..         ...       ...       ...       ...  \n", "971  50.902754  1.679160  1.686606 -0.007446  \n", "972  44.711327  1.677900  1.687553 -0.009653  \n", "973  46.966768  1.674360  1.688543 -0.014183  \n", "974  52.646114  1.678800  1.691559 -0.012759  \n", "975  45.679792  1.687160  1.694249 -0.007089  \n", "\n", "[943 rows x 10 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["data = prepare_data(ohlcv_data, starting_date_backtest, ending_date_backtest)\n", "data"]}, {"cell_type": "markdown", "metadata": {"id": "93cmN-VcSJGf"}, "source": ["<h3>Longs<h3>"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "y4fLfGswTQVs"}, "outputs": [], "source": ["def check_long_entry_condition(row, previous_row):\n", "    return row[\"ao\"] > 0 and previous_row[\"ao\"] < 0\n", "\n", "\n", "def check_long_exit_condition(row, previous_row):\n", "    return row[\"ao\"] < 0 and previous_row[\"ao\"] > 0\n", "\n", "\n", "def compute_long_sl_level(row, entry_price):\n", "    return entry_price * (1 + params[\"tp_pct\"] / 100)\n", "\n", "\n", "def compute_long_tp_level(row, entry_price):\n", "    return entry_price * (1 - params[\"sl_pct\"] / 100)"]}, {"cell_type": "markdown", "metadata": {"id": "lHa8jM6wSJN0"}, "source": ["<h3>Shorts<h3>"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "_JcIMhvOTVBQ"}, "outputs": [], "source": ["def check_short_entry_condition(row, previous_row):\n", "    return row[\"ao\"] < 0 and previous_row[\"ao\"] > 0\n", "\n", "\n", "def check_short_exit_condition(row, previous_row):\n", "    return row[\"ao\"] > 0 and previous_row[\"ao\"] < 0\n", "\n", "\n", "def compute_short_sl_level(row, entry_price):\n", "    return 1000000\n", "\n", "\n", "def compute_short_tp_level(row, entry_price):\n", "    return 0"]}, {"cell_type": "markdown", "metadata": {"id": "XmF1hYyMTcfB"}, "source": ["**<h1>Core Functions<h1>**\n", "\n", "---"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"id": "G8cHv-e9Tytl"}, "outputs": [], "source": ["def calculate_position_size(balance, percentage):\n", "    return balance * percentage / 100\n", "\n", "\n", "def calculate_liquidation_price(price, leverage, order_type):\n", "        if order_type == 'long':\n", "            return price * (1 - 1 / leverage)\n", "        elif order_type == 'short':\n", "            return price * (1 + 1 / leverage)\n", "\n", "\n", "def calculate_pnl(entry_price, exit_price, quantity, order_type):\n", "    if order_type == 'long':\n", "        return (exit_price - entry_price) * quantity\n", "    elif order_type == 'short':\n", "        return (entry_price - exit_price) * quantity"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "vNk2TyQqT8QO"}, "outputs": [], "source": ["def record_order(timestamp, type, price, amount, pnl, wallet, fee, orders):\n", "    order = {\n", "        'timestamp': timestamp,\n", "        'type': type,\n", "        'amount': amount,\n", "        'fee': fee,\n", "        'pnl': pnl,\n", "        'wallet': wallet,\n", "    }\n", "    orders.append(order)\n", "    print(f\"{type} at ${price} on {timestamp}, amount = {round(amount,2)}, pnl = ${round(pnl,2)}, wallet = ${round(wallet,2)}\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "HyqOw-n0UF_K"}, "outputs": [], "source": ["def run_backtest(data):\n", "\n", "    # Initialize variables\n", "    orders = []\n", "    order_in_progress = None\n", "    last_ath = 0\n", "    sl_price = 0\n", "    tp_price = 0\n", "    long_liquidation_price = 0\n", "    short_liquidation_price = 1e10\n", "    wallet = initial_capital\n", "    data['realised_pnl'] = ''\n", "    data['unrealised_pnl'] = ''\n", "    data['hodl'] = ''\n", "    data['drawdown'] = ''\n", "    previous_row = data.iloc[0]\n", "\n", "\n", "    # Go through data and make trades\n", "    for index, row in data.iterrows():\n", "        price = row['close']\n", "\n", "\n", "        # check if it is time to close a long\n", "        if order_in_progress == 'long' and not ignore_longs:\n", "            if row['low'] < long_liquidation_price:\n", "                print(f' /!\\\\ Your long was liquidated on the {row[\"timestamp\"]} (price = {long_liquidation_price} {name_quote})')\n", "                sys.exit()\n", "\n", "            elif not ignore_sl and row['low'] <= sl_price:\n", "                pnl = calculate_pnl(entry_price, sl_price, quantity, order_in_progress)\n", "                fee_exit = quantity * sl_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'long sl', sl_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_tp and row['high'] >= tp_price:\n", "                pnl = calculate_pnl(entry_price, tp_price, quantity, order_in_progress)\n", "                fee_exit = quantity * tp_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'long tp', tp_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_exit and check_long_exit_condition(row, previous_row):\n", "                pnl = calculate_pnl(entry_price, price, quantity, order_in_progress)\n", "                fee_exit = quantity * price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'long exit', price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            if wallet > last_ath:\n", "                last_ath = wallet\n", "\n", "\n", "        # check if it is time to close a short\n", "        elif order_in_progress == 'short' and not ignore_shorts:\n", "            if row['high'] > short_liquidation_price:\n", "                print(f' /!\\\\ Your short was liquidated on the {row[\"timestamp\"]} (price = {short_liquidation_price} {name_quote})')\n", "                sys.exit()\n", "\n", "            elif not ignore_sl and row['high'] >= sl_price:\n", "                pnl = calculate_pnl(entry_price, sl_price, quantity, order_in_progress)\n", "                fee_exit = quantity * sl_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'short sl', sl_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_tp and row['low'] <= tp_price:\n", "                pnl = calculate_pnl(entry_price, tp_price, quantity, order_in_progress)\n", "                fee_exit = quantity * tp_price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'short tp', tp_price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            elif not ignore_exit and check_short_exit_condition(row, previous_row):\n", "                pnl = calculate_pnl(entry_price, price, quantity, order_in_progress)\n", "                fee_exit = quantity * price * trade_fees / 100\n", "                wallet += position - fee_entry + pnl - fee_exit\n", "                record_order(row['timestamp'], 'short exit', price, 0, pnl - fee_exit - fee_entry, wallet, fee_exit, orders)\n", "                order_in_progress = None\n", "\n", "            if wallet > last_ath:\n", "                last_ath = wallet\n", "\n", "\n", "        # check it is time to enter a long\n", "        if not ignore_longs and order_in_progress == None:\n", "            if check_long_entry_condition(row, previous_row):\n", "                order_in_progress = 'long'\n", "                if not ignore_sl:\n", "                    sl_price = compute_long_sl_level(row, price)\n", "                if not ignore_tp:\n", "                    tp_price = compute_long_tp_level(row, price)\n", "                entry_price = price\n", "                position = calculate_position_size(wallet, position_size_pct)\n", "                amount = position * leverage\n", "                fee_entry = amount * trade_fees / 100\n", "                quantity = (amount - fee_entry) / price\n", "                long_liquidation_price = calculate_liquidation_price(price, leverage, order_in_progress)\n", "                if wallet > last_ath:\n", "                    last_ath = wallet\n", "\n", "                wallet -= position\n", "                record_order(row['timestamp'], 'long entry', price, amount-fee_entry, -fee_entry, wallet, fee_entry, orders)\n", "\n", "\n", "        # check if it is time to enter a short\n", "        if not ignore_shorts and order_in_progress == None:\n", "            if check_short_entry_condition(row, previous_row):\n", "                order_in_progress = 'short'\n", "                if not ignore_sl:\n", "                    sl_price = compute_short_sl_level(row, price)\n", "                if not ignore_tp:\n", "                    tp_price = compute_short_tp_level(row, price)\n", "                entry_price = price\n", "                position = calculate_position_size(wallet, position_size_pct)\n", "                amount = position * leverage\n", "                fee_entry = amount * trade_fees / 100\n", "                quantity = (amount - fee_entry) / price\n", "                short_liquidation_price = calculate_liquidation_price(price, leverage, order_in_progress)\n", "                wallet -= position\n", "                record_order(row['timestamp'], 'short entry', price, amount-fee_entry, -fee_entry, wallet, fee_entry, orders)\n", "\n", "\n", "        # updating wallet info\n", "        data.at[index, 'realised_pnl'] = wallet\n", "        data.at[index, 'unrealised_pnl'] = data.at[index, 'realised_pnl']\n", "        if order_in_progress != None:\n", "            data.at[index, 'unrealised_pnl'] += position + calculate_pnl(entry_price, price, quantity, order_in_progress) #- fee\n", "        data.at[index, 'hodl'] = initial_capital / data[\"close\"].iloc[0] * price\n", "        data.at[index, 'drawdown'] = (data.at[index, 'unrealised_pnl'] - last_ath) / last_ath if last_ath else 0\n", "\n", "        previous_row = row\n", "\n", "    return data, orders"]}, {"cell_type": "markdown", "metadata": {"id": "sBhyHAFlUWXo"}, "source": ["**<h1>Backtest<h1>**\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "Cqrd41wNSJRW"}, "source": ["<h3>Run<h3>"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"id": "5a2mV43YUgsj"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["long entry at $0.62883 on 2025-09-19 12:54:00, amount = 999.55, pnl = $-0.45, wallet = $0.0\n", "long exit at $0.65999 on 2025-09-19 14:48:00, amount = 0, pnl = $48.61, wallet = $1048.61\n", "long entry at $0.67844 on 2025-09-19 16:09:00, amount = 1048.14, pnl = $-0.47, wallet = $0.0\n", "long exit at $0.77374 on 2025-09-19 18:48:00, amount = 0, pnl = $146.22, wallet = $1194.83\n", "long entry at $0.78389 on 2025-09-19 20:54:00, amount = 1194.29, pnl = $-0.54, wallet = $0.0\n", "long exit at $0.81679 on 2025-09-19 22:24:00, amount = 0, pnl = $49.03, wallet = $1243.86\n", "long entry at $0.82051 on 2025-09-19 22:30:00, amount = 1243.3, pnl = $-0.56, wallet = $0.0\n", "long exit at $0.88506 on 2025-09-20 01:09:00, amount = 0, pnl = $96.65, wallet = $1340.5\n", "long entry at $0.943 on 2025-09-20 01:48:00, amount = 1339.9, pnl = $-0.6, wallet = $0.0\n", "long exit at $0.9451 on 2025-09-20 03:36:00, amount = 0, pnl = $1.78, wallet = $1342.28\n", "long entry at $0.95369 on 2025-09-20 04:48:00, amount = 1341.68, pnl = $-0.6, wallet = $0.0\n", "long exit at $0.92581 on 2025-09-20 05:09:00, amount = 0, pnl = $-40.41, wallet = $1301.87\n", "long entry at $0.93283 on 2025-09-20 07:00:00, amount = 1301.28, pnl = $-0.59, wallet = $0.0\n", "long exit at $1.1489 on 2025-09-20 11:06:00, amount = 0, pnl = $300.11, wallet = $1601.97\n", "long entry at $1.2452 on 2025-09-20 11:21:00, amount = 1601.25, pnl = $-0.72, wallet = $0.0\n", "long exit at $1.1808 on 2025-09-20 11:57:00, amount = 0, pnl = $-84.22, wallet = $1517.76\n", "long entry at $1.2017 on 2025-09-20 12:27:00, amount = 1517.07, pnl = $-0.68, wallet = $0.0\n", "long exit at $1.1818 on 2025-09-20 12:48:00, amount = 0, pnl = $-26.48, wallet = $1491.28\n", "long entry at $1.2061 on 2025-09-20 13:45:00, amount = 1490.61, pnl = $-0.67, wallet = $0.0\n", "long exit at $1.1773 on 2025-09-20 14:00:00, amount = 0, pnl = $-36.92, wallet = $1454.36\n", "long entry at $1.2016 on 2025-09-20 14:21:00, amount = 1453.7, pnl = $-0.65, wallet = $0.0\n", "long exit at $1.231 on 2025-09-20 15:57:00, amount = 0, pnl = $34.24, wallet = $1488.6\n", "long entry at $1.2598 on 2025-09-20 16:39:00, amount = 1487.93, pnl = $-0.67, wallet = $-0.0\n", "long exit at $1.5523 on 2025-09-20 19:48:00, amount = 0, pnl = $343.97, wallet = $1832.58\n", "long entry at $1.57 on 2025-09-20 21:15:00, amount = 1831.75, pnl = $-0.82, wallet = $0.0\n", "long exit at $1.5474 on 2025-09-20 21:48:00, amount = 0, pnl = $-28.0, wallet = $1804.57\n", "long entry at $1.6014 on 2025-09-20 22:00:00, amount = 1803.76, pnl = $-0.81, wallet = $-0.0\n", "long exit at $1.6474 on 2025-09-20 23:15:00, amount = 0, pnl = $50.17, wallet = $1854.74\n", "long entry at $1.6649 on 2025-09-20 23:48:00, amount = 1853.9, pnl = $-0.83, wallet = $0.0\n", "long exit at $1.6039 on 2025-09-21 01:00:00, amount = 0, pnl = $-69.56, wallet = $1785.17\n", "long entry at $1.6416 on 2025-09-21 02:27:00, amount = 1784.37, pnl = $-0.8, wallet = $-0.0\n", "long exit at $1.5793 on 2025-09-21 02:45:00, amount = 0, pnl = $-69.29, wallet = $1715.88\n", "long entry at $1.6018 on 2025-09-21 03:09:00, amount = 1715.11, pnl = $-0.77, wallet = $0.0\n", "long exit at $1.778 on 2025-09-21 05:30:00, amount = 0, pnl = $187.04, wallet = $1902.91\n", "long entry at $1.8227 on 2025-09-21 05:45:00, amount = 1902.06, pnl = $-0.86, wallet = $0.0\n", "long exit at $1.8848 on 2025-09-21 07:27:00, amount = 0, pnl = $63.06, wallet = $1965.98\n", "long entry at $1.7 on 2025-09-21 10:21:00, amount = 1965.09, pnl = $-0.88, wallet = $0.0\n", "long exit at $1.6894 on 2025-09-21 10:54:00, amount = 0, pnl = $-14.02, wallet = $1951.96\n"]}], "source": ["data, backtest_orders = run_backtest(data)"]}, {"cell_type": "markdown", "metadata": {"id": "UYxUw_-NSJUq"}, "source": ["<h3>Analysis<h3>"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "jFWDuCjSUrIi"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" \n", "\n", "      ** Profits ** \n", "\n", " > Period: 2025-09-19 12:09:00 -> 2025-09-21 11:15:00 \n", " > Starting balance: 1000 USDC\n", " > Final balance Bot: 1951.96 USDC\n", " > Final balance Hodl: 2559.76 USDC\n", " > Bot net profits: 95.2%\n", " > Hodl net profits: 155.98%\n", " > Net profits ratio Bot / Hodl: 0.76\n", " \n", "\n", "      ** Trades ** \n", "\n", " > Orders: 38 (19 buys, 19 sells)\n", " > Number of closed trades: 19\n", " > Winrate: 57.89%\n", " > Average trade profits: 3.89%\n", " > Number of winning trades: 11\n", " > Number of losing trades: 8\n", " > Average winning trades: 8.76%\n", " > Average losing trades: -2.8%\n", " > Best trade: 23.11% on the 2025-09-20 19:48:00\n", " > Worst trade: -5.26% on the 2025-09-20 11:57:00\n", " \n", "\n", "      ** Health ** \n", "\n", " > Maximum drawdown: -9.34%\n", " > Profit factor: 3.58\n", " > Return over maximum drawdown: 10.19\n", " \n", "\n", "      ** Fees ** \n", "\n", " > Total: 26.43 USDC\n", " > Biggest: 0.89 USDC\n", " > Average: 0.7 USDC \n", "\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "## Profits\n", "show_unrealised = True\n", "show_realised = False\n", "show_hodl = True\n", "\n", "profits_bot_realised = ((data['realised_pnl'] - initial_capital)/initial_capital) * 100\n", "profits_bot_unrealised = ((data['unrealised_pnl'] - initial_capital)/initial_capital) * 100\n", "profits_hodl = ((data['hodl'] - data.iloc[0]['hodl'])/data.iloc[0]['hodl']) * 100\n", "\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 10))\n", "if show_unrealised:\n", "    ax1.plot(data['timestamp'], profits_bot_unrealised, color='gold', label='Bot')\n", "if show_realised:\n", "    ax1.plot(data['timestamp'], profits_bot_realised, color='gold', label='Bot (realised)', ls= '--')\n", "if show_hodl:\n", "    ax1.plot(data['timestamp'], profits_hodl, color='purple', label='Hodl')\n", "ax1.set_title('Net Profits', fontsize=20)\n", "ax1.set_ylabel('Net Profits (%)', fontsize=18)\n", "ax1.set_xticklabels([])\n", "ax1.legend(fontsize=16)\n", "if show_unrealised:\n", "    ax2.plot(data['timestamp'], data['unrealised_pnl'], color='gold', label='Bot')\n", "if show_realised:\n", "    ax2.plot(data['timestamp'], data['realised_pnl'], color='gold', label='Bot (realised)', ls= '--')\n", "if show_hodl:\n", "    ax2.plot(data['timestamp'], data['hodl'], color='purple', label='Hodl')\n", "ax2.set_xlabel('Period', fontsize=18)\n", "ax2.set_ylabel('Net Profits (' + name_quote + ')', fontsize=18)\n", "ax2.tick_params(axis='both', which='major', labelsize=12, rotation = 45)\n", "\n", "print(f\" \\n\\n      ** Profits ** \\n\")\n", "print(f\" > Period: {data['timestamp'].iloc[0]} -> {data['timestamp'].iloc[-1]} \")\n", "print(f\" > Starting balance: {initial_capital} {name_quote}\")\n", "print(f\" > Final balance Bot: {round(data.iloc[-1]['unrealised_pnl'],2)} {name_quote}\")\n", "print(f\" > Final balance Hodl: {round(data.iloc[-1]['hodl'],2)} {name_quote}\")\n", "print(f\" > Bot net profits: {round(profits_bot_unrealised.iloc[-1],2)}%\")\n", "print(f\" > Hodl net profits: {round(profits_hodl.iloc[-1],2)}%\")\n", "print(f\" > Net profits ratio Bot / Hodl: {round(data.iloc[-1]['unrealised_pnl']/data.iloc[-1]['hodl'],2)}\")\n", "\n", "\n", "## Trades\n", "orders = pd.json_normalize(backtest_orders, sep='_')\n", "n_orders = len(orders.index)\n", "if not ignore_longs:\n", "    n_longs = orders['type'].value_counts()['long entry']\n", "else:\n", "    n_longs = 0\n", "if not ignore_shorts:\n", "    n_shorts = orders['type'].value_counts()['short entry']\n", "else:\n", "    n_shorts = 0\n", "n_entry_orders = 0\n", "if not ignore_longs:\n", "    n_entry_orders += orders['type'].value_counts()['long entry']\n", "if not ignore_shorts:\n", "    n_entry_orders += orders['type'].value_counts()['short entry']\n", "\n", "n_exit_orders = 0\n", "if 'long exit' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['long exit']\n", "if 'long tp' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['long tp']\n", "if 'long sl' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['long sl']\n", "if 'short exit' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['short exit']\n", "if 'short tp' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['short tp']\n", "if 'short sl' in orders['type'].value_counts():\n", "    n_exit_orders += orders['type'].value_counts()['short sl']\n", "\n", "orders.loc[::2, 'pnl'] = np.nan\n", "orders['Win'] = ''\n", "orders.loc[orders['pnl']>0,'Win'] = 'Yes'\n", "orders.loc[orders['pnl']<=0,'Win'] = 'No'\n", "if 'Yes' in orders['Win'].value_counts():\n", "    n_pos_trades = orders['Win'].value_counts()['Yes']\n", "else:\n", "    n_pos_trades = 0\n", "if 'No' in orders['Win'].value_counts():\n", "    n_neg_trades = orders['Win'].value_counts()['No']\n", "else:\n", "    n_neg_trades = 0\n", "\n", "winrate = round(n_pos_trades / (n_pos_trades+n_neg_trades) * 100,2)\n", "orders['pnl%'] = orders['pnl'] / (orders['wallet'] - orders['pnl'])  * 100\n", "avg_trades = round(orders['pnl%'].mean(),2)\n", "avg_pos_trades = round(orders.loc[orders['Win'] == 'Yes']['pnl%'].mean(),2)\n", "avg_neg_trades = round(orders.loc[orders['Win'] == 'No']['pnl%'].mean(),2)\n", "best_trade = orders['pnl%'].max()\n", "when_best_trade = orders['timestamp'][orders.loc[orders['pnl%'] == best_trade].index.tolist()[0]]\n", "best_trade = round(best_trade,2)\n", "worst_trade = orders['pnl%'].min()\n", "when_worst_trade = orders['timestamp'][orders.loc[orders['pnl%'] == worst_trade].index.tolist()[0]]\n", "worst_trade = round(worst_trade,2)\n", "\n", "print(f\" \\n\\n      ** Trades ** \\n\")\n", "print(f\" > Orders: {n_orders} ({n_entry_orders} buys, {n_exit_orders} sells)\")\n", "print(f\" > Number of closed trades: {n_pos_trades+n_neg_trades}\")\n", "print(f\" > Winrate: {winrate}%\")\n", "print(f\" > Average trade profits: {avg_trades}%\")\n", "print(f\" > Number of winning trades: {n_pos_trades}\")\n", "print(f\" > Number of losing trades: {n_neg_trades}\")\n", "print(f\" > Average winning trades: {avg_pos_trades}%\")\n", "print(f\" > Average losing trades: {avg_neg_trades}%\")\n", "print(f\" > Best trade: {best_trade}% on the {when_best_trade}\")\n", "print(f\" > Worst trade: {worst_trade}% on the {when_worst_trade}\")\n", "\n", "\n", "## Health\n", "worst_drawdown = round(data['drawdown'].min()*100,2)\n", "profit_factor = round(abs(orders.loc[orders['pnl'] > 0, 'pnl'].sum() / orders.loc[orders['pnl'] < 0, 'pnl'].sum()),2)\n", "return_over_max_drawdown = round(profits_bot_unrealised.iloc[-1] / abs(worst_drawdown),2)\n", "\n", "print(f\" \\n\\n      ** Health ** \\n\")\n", "print(f\" > Maximum drawdown: {worst_drawdown}%\")\n", "print(f\" > Profit factor: {profit_factor}\")\n", "print(f\" > Return over maximum drawdown: {return_over_max_drawdown}\")\n", "\n", "\n", "## fees\n", "total_fee = round(orders['fee'].sum(),2)\n", "biggest_fee = round(orders['fee'].max(),2)\n", "avg_fee = round(orders['fee'].mean(),2)\n", "\n", "print(f\" \\n\\n      ** Fees ** \\n\")\n", "print(f\" > Total: {total_fee} {name_quote}\")\n", "print(f\" > Biggest: {biggest_fee} {name_quote}\")\n", "print(f\" > Average: {avg_fee} {name_quote} \\n\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 1}