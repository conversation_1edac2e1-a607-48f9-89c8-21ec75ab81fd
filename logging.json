{"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}}, "handlers": {"console": {"class": "logging.StreamHandler", "formatter": "default", "level": "INFO", "stream": "ext://sys.stdout"}, "file": {"class": "logging.FileHandler", "formatter": "default", "level": "DEBUG", "filename": "freqtrade.log", "mode": "a"}}, "root": {"handlers": ["console", "file"], "level": "DEBUG"}}