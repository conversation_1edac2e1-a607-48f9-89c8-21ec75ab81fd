#!/usr/bin/env python3
"""
Simple script to run LSTM strategy backtests with correct configurations.
"""

import subprocess
import sys
import os
from datetime import datetime

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n🚀 {description}")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, check=True, text=True, capture_output=True)
        print("✅ Success!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stderr:
            print(f"Error details: {e.stderr}")
        return False

def download_data(timeframe="5m"):
    """Download data for the specified timeframe."""
    if timeframe == "5m":
        config = "config_lstm_5m.json"
        timeframes = ["5m", "15m", "1h"]
    else:  # 15m
        config = "config_lstm_optimized.json"
        timeframes = ["15m", "1h", "4h"]
    
    cmd = [
        "freqtrade", "download-data",
        "--config", config,
        "--timerange", "20241001-20241201",  # Recent 2 months
        "--timeframe"] + timeframes + [
        "--pairs", "ETH/USDT:USDT", "BTC/USDT:USDT",
        "--trading-mode", "futures",
        "--erase"
    ]
    
    return run_command(cmd, f"Downloading data for {timeframe} strategy")

def run_backtest(timeframe="5m"):
    """Run backtest for the specified timeframe."""
    if timeframe == "5m":
        config = "config_lstm_5m.json"
        strategy = "OptimizedLSTMStrategy"
    else:  # 15m
        config = "config_lstm_optimized.json"
        strategy = "OptimizedLSTMStrategy15m"
    
    cmd = [
        "freqtrade", "backtesting",
        "--config", config,
        "--strategy", strategy,
        "--freqaimodel", "PyTorchLSTMRegressor",
        "--timerange", "20241001-20241201",
        "--breakdown", "day", "week",
        "--export", "trades"
    ]
    
    return run_command(cmd, f"Running backtest for {timeframe} strategy")

def main():
    """Main function."""
    print("🤖 LSTM Trading Strategy Backtesting Tool")
    print("=" * 50)
    
    # Check if freqtrade is available
    try:
        subprocess.run(["freqtrade", "--version"], capture_output=True, check=True)
        print("✅ Freqtrade found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Freqtrade not found. Please install freqtrade first.")
        sys.exit(1)
    
    # Check required files
    required_files = {
        "5m": [
            "config_lstm_5m.json",
            "strategies/OptimizedLSTMStrategy.py",
            "freqaimodels/PyTorchLSTMRegressor.py"
        ],
        "15m": [
            "config_lstm_optimized.json", 
            "strategies/OptimizedLSTMStrategy15m.py",
            "freqaimodels/PyTorchLSTMRegressor.py"
        ]
    }
    
    print("\n📋 Checking required files...")
    available_timeframes = []
    
    for tf, files in required_files.items():
        missing = [f for f in files if not os.path.exists(f)]
        if missing:
            print(f"❌ {tf} strategy missing files: {', '.join(missing)}")
        else:
            print(f"✅ {tf} strategy files ready")
            available_timeframes.append(tf)
    
    if not available_timeframes:
        print("❌ No complete strategy configurations found!")
        sys.exit(1)
    
    # Choose timeframe
    print(f"\n📊 Available timeframes: {', '.join(available_timeframes)}")
    
    if len(available_timeframes) == 1:
        timeframe = available_timeframes[0]
        print(f"🎯 Using {timeframe} timeframe")
    else:
        while True:
            choice = input(f"\nChoose timeframe ({'/'.join(available_timeframes)}): ").strip().lower()
            if choice in available_timeframes:
                timeframe = choice
                break
            print("❌ Invalid choice. Please try again.")
    
    # Ask what to do
    print(f"\n🛠️ What would you like to do?")
    print("1. Download data only")
    print("2. Run backtest only (requires existing data)")
    print("3. Download data and run backtest")
    
    while True:
        choice = input("\nEnter choice (1/2/3): ").strip()
        if choice in ["1", "2", "3"]:
            break
        print("❌ Invalid choice. Please enter 1, 2, or 3.")
    
    # Execute chosen action
    success = True
    
    if choice in ["1", "3"]:
        success = download_data(timeframe)
    
    if success and choice in ["2", "3"]:
        success = run_backtest(timeframe)
    
    # Summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 All operations completed successfully!")
        print(f"\n📈 Check the backtest results for {timeframe} timeframe")
        print("💡 Next steps:")
        print("   - Review the backtest results")
        print("   - Try hyperparameter optimization if results look good")
        print("   - Test with paper trading before going live")
    else:
        print("❌ Some operations failed. Please check the errors above.")

if __name__ == "__main__":
    main()
