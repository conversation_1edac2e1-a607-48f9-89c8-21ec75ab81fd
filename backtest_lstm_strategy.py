#!/usr/bin/env python3
"""
Comprehensive backtesting script for the Optimized LSTM Strategy.

This script performs extensive backtesting on ETH/USDT with multiple timeframes
and provides detailed analysis of the results.
"""

import os
import sys
import subprocess
import json
import pandas as pd
from datetime import datetime, timedelta
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LSTMBacktester:
    def __init__(self):
        self.base_config = "config_lstm_optimized.json"
        self.strategy = "OptimizedLSTMStrategy"
        self.freqai_model = "PyTorchLSTMRegressor"
        self.pair = "ETH/USDT:USDT"
        self.timeframes = ["5m", "15m"]
        self.results = {}
        
    def download_data(self, timerange="20231201-20241201"):
        """Download required data for backtesting."""
        logger.info("Downloading data for backtesting...")
        
        timeframes_str = " ".join(self.timeframes + ["1h"])  # Add 1h for correlation
        
        cmd = [
            "freqtrade", "download-data",
            "--config", self.base_config,
            "--timerange", timerange,
            "--timeframe", timeframes_str,
            "--pairs", self.pair, "BTC/USDT:USDT",  # Include BTC for correlation
            "--trading-mode", "futures",
            "--erase"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info("Data download completed successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Data download failed: {e.stderr}")
            return False
    
    def run_backtest(self, timeframe, timerange="20231201-20241201", config_override=None):
        """Run backtest for specific timeframe."""
        logger.info(f"Running backtest for {timeframe} timeframe...")
        
        # Create timeframe-specific config
        config_file = f"config_lstm_{timeframe}.json"
        self.create_timeframe_config(timeframe, config_file, config_override)
        
        cmd = [
            "freqtrade", "backtesting",
            "--config", config_file,
            "--strategy", self.strategy,
            "--freqaimodel", self.freqai_model,
            "--timerange", timerange,
            "--breakdown", "day", "week", "month",
            "--export", "trades",
            "--export-filename", f"backtest_lstm_{timeframe}_{timerange}"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"Backtest completed for {timeframe}")
            
            # Parse results
            output_lines = result.stdout.split('\n')
            results = self.parse_backtest_output(output_lines)
            self.results[timeframe] = results
            
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Backtest failed for {timeframe}: {e.stderr}")
            return False
    
    def create_timeframe_config(self, timeframe, config_file, config_override=None):
        """Create timeframe-specific configuration."""
        with open(self.base_config, 'r') as f:
            config = json.load(f)
        
        # Update timeframe-specific settings
        if timeframe == "5m":
            config["freqai"]["train_period_days"] = 30
            config["freqai"]["backtest_period_days"] = 7
            config["freqai"]["feature_parameters"]["label_period_candles"] = 12
            config["freqai"]["model_training_parameters"]["trainer_kwargs"]["n_epochs"] = 80
        elif timeframe == "15m":
            config["freqai"]["train_period_days"] = 45
            config["freqai"]["backtest_period_days"] = 10
            config["freqai"]["feature_parameters"]["label_period_candles"] = 20
            config["freqai"]["model_training_parameters"]["trainer_kwargs"]["n_epochs"] = 100
        
        # Apply any additional overrides
        if config_override:
            config.update(config_override)
        
        # Update identifier
        config["freqai"]["identifier"] = f"lstm_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M')}"
        
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
    
    def parse_backtest_output(self, output_lines):
        """Parse backtest output to extract key metrics."""
        results = {}
        
        for line in output_lines:
            if "Total Profit" in line:
                results['total_profit'] = line.split(':')[-1].strip()
            elif "Total trades" in line:
                results['total_trades'] = line.split(':')[-1].strip()
            elif "Win/Loss" in line:
                results['win_loss'] = line.split(':')[-1].strip()
            elif "Avg. Duration" in line:
                results['avg_duration'] = line.split(':')[-1].strip()
            elif "Max Drawdown" in line:
                results['max_drawdown'] = line.split(':')[-1].strip()
            elif "Profit factor" in line:
                results['profit_factor'] = line.split(':')[-1].strip()
            elif "Sharpe Ratio" in line:
                results['sharpe_ratio'] = line.split(':')[-1].strip()
        
        return results
    
    def run_hyperopt(self, timeframe, epochs=100, timerange="20231201-20241201"):
        """Run hyperparameter optimization."""
        logger.info(f"Running hyperopt for {timeframe} timeframe...")
        
        config_file = f"config_lstm_{timeframe}.json"
        
        cmd = [
            "freqtrade", "hyperopt",
            "--config", config_file,
            "--strategy", self.strategy,
            "--freqaimodel", self.freqai_model,
            "--hyperopt-loss", "SharpeHyperOptLoss",
            "--spaces", "buy", "sell",
            "--epochs", str(epochs),
            "--timerange", timerange,
            "--jobs", "1"  # FreqAI requires single job
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"Hyperopt completed for {timeframe}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Hyperopt failed for {timeframe}: {e.stderr}")
            return False
    
    def generate_report(self):
        """Generate comprehensive backtesting report."""
        logger.info("Generating backtesting report...")
        
        report = []
        report.append("=" * 80)
        report.append("OPTIMIZED LSTM STRATEGY - BACKTESTING REPORT")
        report.append("=" * 80)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Strategy: {self.strategy}")
        report.append(f"Model: {self.freqai_model}")
        report.append(f"Pair: {self.pair}")
        report.append("")
        
        for timeframe, results in self.results.items():
            report.append(f"TIMEFRAME: {timeframe}")
            report.append("-" * 40)
            for key, value in results.items():
                report.append(f"{key.replace('_', ' ').title()}: {value}")
            report.append("")
        
        # Save report
        report_file = f"lstm_backtest_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w') as f:
            f.write('\n'.join(report))
        
        logger.info(f"Report saved to {report_file}")
        
        # Print summary
        print('\n'.join(report))
    
    def run_comprehensive_test(self):
        """Run comprehensive backtesting suite."""
        logger.info("Starting comprehensive LSTM strategy backtesting...")
        
        # Step 1: Download data
        if not self.download_data():
            logger.error("Failed to download data. Exiting.")
            return False
        
        # Step 2: Run backtests for each timeframe
        for timeframe in self.timeframes:
            if not self.run_backtest(timeframe):
                logger.warning(f"Backtest failed for {timeframe}, continuing...")
                continue
        
        # Step 3: Generate report
        self.generate_report()
        
        # Step 4: Optional hyperopt (commented out for speed)
        # for timeframe in self.timeframes:
        #     self.run_hyperopt(timeframe, epochs=50)
        
        logger.info("Comprehensive backtesting completed!")
        return True

def main():
    """Main execution function."""
    print("🚀 Starting Optimized LSTM Strategy Backtesting")
    print("=" * 60)
    
    # Check if freqtrade is available
    try:
        subprocess.run(["freqtrade", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Freqtrade not found. Please install freqtrade first.")
        sys.exit(1)
    
    # Check if required files exist
    required_files = [
        "config_lstm_optimized.json",
        "strategies/OptimizedLSTMStrategy.py",
        "freqaimodels/PyTorchLSTMRegressor.py"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Required file not found: {file}")
            sys.exit(1)
    
    # Run backtesting
    backtester = LSTMBacktester()
    success = backtester.run_comprehensive_test()
    
    if success:
        print("\n✅ Backtesting completed successfully!")
        print("📊 Check the generated report for detailed results.")
    else:
        print("\n❌ Backtesting failed. Check logs for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
