#!/bin/bash
CONTAINER="964534d72eac"
TM="futures"
TIMERANGE="20220101-"
CONFIG="config"

declare -a strategies=(
"AODualSideStrategy"
#"RefinedInsaneStrategy"
#  "AODualSideStrategySameCandle"
#  "StochRSI_UTBot_EMA_EWO_Feb_2024_1m_long_prod_2"
  #"AggressiveProfitStrategy"
#  "AORsiStrategy"
#  "NostalgiaForInfinityX6"
)

declare -a pairs=(
"ASTER/USDT:USDT"
# "UNI/USDT:USDT"
#  "ETH/USDT:USDT"
#  "ENA/USDT:USDT"
# "DOT/USDT:USDT"
# "GALA/USDT:USDT"
# "SOL/USDT:USDT"
#  "HYPE/USDT:USDT"
#  "MNT/USDT:USDT"
)

declare -a intervals=(
"3m"
"5m"
#  "15m"
#  "1h"
#  "2h"
#  "4h"
  "1d"
)

for pair in "${pairs[@]}"; do
  for interval in "${intervals[@]}"; do
    for strategy in "${strategies[@]}"; do
      echo "--------------------------------------------------------"
      echo "     TESTING ${strategy} - ${interval} - ${pair}"
      echo "--------------------------------------------------------"
      docker compose run --rm freqtrade backtesting \
        --config user_data/${CONFIG}.json \
        --strategy ${strategy} \
        --timerange=${TIMERANGE} \
        -p ${pair} \
        -i ${interval}
#        --freqaimodel LightGBMRegressor \
    done
  done
done
